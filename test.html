<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f5f5f5;
            min-height: 100vh;
        }

        .container {
            min-height: 100vh;
            background: #f5f5f5;
            position: relative;
        }


        .floating-button {
            position: fixed;
            bottom: 24px;
            right: 24px;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background-image: linear-gradient(78deg, #8054f2 7%, #3895da 95%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: 24px;
            font-weight: 600;
            transition: all 0.3s;
            border: none;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1001;
            cursor: pointer;
        }

        .floating-button:hover {
            opacity: 0.8;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
        }

        .drawer {
            position: fixed;
            top: 0;
            right: -60vw;
            width: 60vw;
            height: 100vh;
            background: #fff;
            box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);
            transition: right 0.3s ease;
            z-index: 1000;
            display: flex;
            flex-direction: column;
        }

        .drawer.open {
            right: 0;
        }

        .drawer-header {
            height: 55px;
            border-bottom: 1px solid #e8e8e8;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            background: #fff;
        }

        .drawer-title {
            font-size: 16px;
            font-weight: 600;
            color: #262626;
        }

        .drawer-close {
            background: none;
            border: none;
            font-size: 16px;
            cursor: pointer;
            color: #8c8c8c;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s;
        }

        .drawer-close:hover {
            background: #f5f5f5;
            color: #262626;
        }

        .drawer-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .drawer-iframe {
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 8px;
        }

        .drawer-mask {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.45);
            z-index: 998;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .drawer-mask.show {
            opacity: 1;
            visibility: visible;
        }

        /* 图标样式 */
        .icon {
            width: 1em;
            height: 1em;
            fill: currentColor;
        }
    </style>
</head>
<body>
<div class="container">
    <!-- 浮动按钮 -->
    <button class="floating-button" onclick="openDrawer()">
        <svg class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"/>
            <path d="M512 336c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm0 288c-61.9 0-112-50.1-112-112s50.1-112 112-112 112 50.1 112 112-50.1 112-112 112z"/>
        </svg>
    </button>

    <!-- 遮罩层 -->
    <div class="drawer-mask" onclick="closeDrawer()"></div>

    <!-- 侧边栏 -->
    <div class="drawer" id="drawer">
        <div class="drawer-header">
            <button class="drawer-close" onclick="closeDrawer()">
                <svg class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                    <path d="M563.8 512l262.5-312.9c4.4-5.2.7-13.1-6.1-13.1h-79.8c-1.3 0-2.6.3-3.8 1L512.6 449.8 295.1 191.7c-1.2-.8-2.5-1-3.8-1h-79.8c-6.8 0-10.5 7.9-6.1 13.1L459.4 512 196.9 824.9A7.95 7.95 0 00203 838h79.8c1.3 0 2.6-.3 3.8-1l217.5-258.1 217.5 258.1c1.2.8 2.5 1 3.8 1h79.8c6.8 0 10.5-7.9 6.1-13.1L563.8 512z"/>
                </svg>
            </button>
        </div>
        <div class="drawer-content">
            <iframe
                    src="http://***********"
                    class="drawer-iframe"
                    title="在线项目"
            ></iframe>
        </div>
    </div>
</div>

<script>
    function openDrawer() {
        const drawer = document.getElementById('drawer');
        const mask = document.querySelector('.drawer-mask');
        const button = document.querySelector('.floating-button');

        drawer.classList.add('open');
        mask.classList.add('show');

        // 隐藏浮动按钮
        button.style.display = 'none';

        // 禁止背景滚动
        document.body.style.overflow = 'hidden';
    }

    function closeDrawer() {
        const drawer = document.getElementById('drawer');
        const mask = document.querySelector('.drawer-mask');
        const button = document.querySelector('.floating-button');

        drawer.classList.remove('open');
        mask.classList.remove('show');

        // 显示浮动按钮
        button.style.display = 'block';

        // 恢复背景滚动
        document.body.style.overflow = 'auto';
    }

    // 键盘事件监听
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            closeDrawer();
        }
    });
</script>
</body>
</html>
