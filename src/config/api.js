// 本地API配置
export const API_CONFIG = {
  // 本地API基础URL（通过代理访问）
  BASE_URL: '/api',
  
  // API端点
  ENDPOINTS: {
    CHAT_COMPLETIONS: '/tuition/chat/completions',
    GET_CHAT_RECORDS: '/tuition/chat/getPaginationRecords',
    GET_CHAT_HISTORIES: '/tuition/chat/getHistories',
    UPDATE_HISTORY: '/tuition/chat/updateHistory',
    DELETE_HISTORY: '/tuition/chat/delHistory',
    LOGIN: '/account/auth/password/login',
    WHITE_AUTH: '/tuition/ip-whitelist/auth/check-ip-whitelist',
    GET_UUID: '/tuition/chat/issueUuid'
  },
  
  // 请求头设置
  getHeaders() {
    return {
      'Content-Type': 'application/json'
    };
  }
};

// 本地存储键名
export const STORAGE_KEYS = {
  CHAT_HISTORY: 'chat_history_',
  CONVERSATIONS: 'conversations'
}; 
