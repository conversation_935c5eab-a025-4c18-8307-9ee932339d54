import axios from "axios";
import {API_CONFIG} from "../config/api.js";
import { ElMessage } from "element-plus";
import router from "../router/index.js";

const instance = axios.create({
    baseURL: API_CONFIG.BASE_URL,
    headers: API_CONFIG.getHeaders(),
    timeout: 150000
});


instance.interceptors.request.use(config => {
    // 获取token并添加到请求头
    const token = localStorage.getItem('auth_token');
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    
    // 获取UUID并添加到请求头
    const uuid = localStorage.getItem('uuid');
    if (uuid) {
        config.headers['X-Encrypted-Uuid'] = uuid;
    }
    
    return config;
}, error => {
    return Promise.reject(error);
});

// 防重复跳转标记
let isRedirecting = false;

// 重置跳转标记
export const resetRedirectFlag = () => {
    isRedirecting = false;
};

instance.interceptors.response.use(response => {
    console.log("response.data",response.data);
    return response.data;
}, error => {
    if(error.response && error.response.status === 401 && !isRedirecting && !window.isGlobalRedirecting){
        isRedirecting = true;
        window.isGlobalRedirecting = true;
        localStorage.removeItem('auth_token');
        ElMessage.error('无权限，请重新登录');
        
        // 使用router.push进行导航，避免页面刷新
        setTimeout(() => {
            if (router.currentRoute.value.path !== '/login') {
                router.push('/login');
            }
        }, 100);
    }
    console.log("error",error.response?.data);
    return Promise.reject(error);
});

// 本地API请求方法
export const localAPI = {
    // 发送聊天请求
    async sendChatMessage(chatId, messages, isDeepThinking = false) {
        return instance.post(API_CONFIG.ENDPOINTS.CHAT_COMPLETIONS + `?chatId=${chatId}&isDeepThinking=${isDeepThinking}`, {
            messages
        });
    },

    // 获取聊天记录 - 使用GET请求
    async getChatRecords(chatId, offset = 0, pageSize = 50) {
        const response = await instance.post(API_CONFIG.ENDPOINTS.GET_CHAT_RECORDS + `?chatId=${chatId}&offset=${offset}&pageSize=${pageSize}`);

        // 如果返回的是新的结构格式，直接返回
        if (response && response.data && response.data.code === 200) {
            return response.data;
        }

        // 否则返回原始响应
        return response;
    },

    // 获取聊天历史列表
    async getChatHistories(offset = 0, pageSize = 20) {
        return instance.post(API_CONFIG.ENDPOINTS.GET_CHAT_HISTORIES, {
            offset,
            pageSize
        });
    },

    // 更新会话标题
    async updateHistoryTitle(chatId, customTitle) {
        return instance.post(API_CONFIG.ENDPOINTS.UPDATE_HISTORY + `?chatId=${chatId}&customTitle=${customTitle}`);
    },

    // 删除会话
    async deleteHistory(chatId) {
        return instance.post(API_CONFIG.ENDPOINTS.DELETE_HISTORY + `?chatId=${chatId}`);
    },

    // 登录
    async login(username, password) {
        const data = new FormData();
        data.append('username', username);
        data.append('password', password);
        data.append('scope', 'pc');

        const config = {
            method: 'post',
            url: API_CONFIG.ENDPOINTS.LOGIN + '/1',
            headers: {
                'Content-Type': 'multipart/form-data',
            },
            data: data
        };

        return instance(config);
    },

    // 白名单验证
    async isWhiteAuth() {
        return instance.get(API_CONFIG.ENDPOINTS.WHITE_AUTH);
    },

    // 获取UUID
    async getUuid() {
        return instance.post(API_CONFIG.ENDPOINTS.GET_UUID);
    }
};

export default instance;
