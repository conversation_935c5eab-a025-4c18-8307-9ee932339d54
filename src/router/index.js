import {createRouter, createWebHistory} from 'vue-router'
import Login from '../components/Login.vue'
import {ElMessage} from "element-plus";
import {localAPI} from "../service/req.js";
import NewLayout from "../components/NewLayout.vue";

// 防重复跳转标记
let isRedirecting = false;

// 全局跳转标记，防止路由守卫和响应拦截器同时跳转
window.isGlobalRedirecting = false;

// 缓存IP白名单检查结果，避免重复请求
let ipWhitelistCache = {
    result: null,
    timestamp: 0,
    error: null
};

// 缓存时间：5分钟
const CACHE_DURATION = 5 * 60 * 1000;

const routes = [
    {
        path: '/',
        redirect: '/chat'
    },
    {
        path: '/login',
        name: 'Login',
        component: Login
    },
    {
        path: '/chat',
        name: 'Chat',
        component: NewLayout
    }
]

const router = createRouter({
    history: createWebHistory(),
    routes
})

// 检查IP白名单的函数
async function checkIpWhitelist() {
    const now = Date.now();

    // 如果有缓存且未过期，直接返回缓存结果
    if (ipWhitelistCache.result !== null &&
        (now - ipWhitelistCache.timestamp) < CACHE_DURATION) {
        console.log('使用缓存的IP白名单检查结果:', ipWhitelistCache.result);
        return ipWhitelistCache.result;
    }

    // 如果有错误缓存且未过期，直接返回错误
    if (ipWhitelistCache.error &&
        (now - ipWhitelistCache.timestamp) < CACHE_DURATION) {
        console.log('使用缓存的IP白名单错误:', ipWhitelistCache.error);
        return false;
    }

    try {
        console.log('执行IP白名单检查...');
        const res = await localAPI.isWhiteAuth();
        console.log('IP白名单检查结果:', res);

        let isValidIp = false;
        if (res && res.code === 200 && res.data.isInWhitelist) {
            isValidIp = true;
        }

        // 缓存成功结果
        ipWhitelistCache = {
            result: isValidIp,
            timestamp: now,
            error: null
        };

        return isValidIp;
    } catch (error) {
        console.error('IP白名单检查失败:', error);

        // 缓存错误结果
        ipWhitelistCache = {
            result: null,
            timestamp: now,
            error: error.message
        };

        return false;
    }
}

// 路由守卫
router.beforeEach(async (to, from, next) => {
    console.log('路由守卫触发:', {to: to.path, from: from.path});

    if (to.path === '/policy-ai') {
        next();
        return;
    }

    // 防止重复触发
    if (isRedirecting || window.isGlobalRedirecting) {
        console.log('正在跳转中，跳过守卫');
        next();
        return;
    }

    // 防止相同路径跳转
    if (to.path === from.path) {
        console.log('相同路径跳转，跳过守卫');
        next();
        return;
    }

    try {
        // 检查IP白名单
        const isValidIp = await checkIpWhitelist();
        console.log('IP有效性检查结果:', isValidIp);

        const token = localStorage.getItem('auth_token');
        console.log('Token检查:', !!token, '目标路径:', to.path);

        // 如果访问登录页
        if (to.path === '/login') {
            // 如果IP在白名单内或有token，重定向到聊天页
            if (isValidIp || token) {
                console.log('用户已登录或IP在白名单内，重定向到聊天页');
                isRedirecting = true;
                window.isGlobalRedirecting = true;
                next('/chat');
            } else {
                console.log('用户未登录且IP不在白名单内，允许访问登录页');
                next();
            }
            return;
        }

        // 如果IP在白名单内
        if (isValidIp) {
            console.log('IP在白名单内，允许访问');
            // 获取uuid（只有在有权限时才获取）
            const uuid = localStorage.getItem('uuid');
            if (!uuid) {
                console.log('获取UUID...');
                try {
                    const res = await localAPI.getUuid();
                    console.log('获取UUID结果:', res);
                    if (res && res.data && res.data.encryptedUuid) {
                        localStorage.setItem('uuid', res.data.encryptedUuid);
                    } else {
                        console.error('获取UUID失败，响应格式不正确:', res);
                    }
                } catch (error) {
                    console.error('获取UUID失败:', error);
                    // UUID获取失败不影响页面访问
                }
            }
            next();
            return;
        }

        // 如果IP不在白名单内，但有token
        if (token) {
            console.log('IP不在白名单内但有token，允许访问');
            next();
            return;
        }

        // IP不在白名单内且无token，跳转到登录页
        console.log('IP不在白名单内且无token，跳转到登录页');
        isRedirecting = true;
        window.isGlobalRedirecting = true;
        ElMessage.error('IP地址无效或未登录，无法访问');
        next('/login');

    } catch (error) {
        console.error('路由守卫执行错误:', error);
        // 如果API请求失败，但有token，允许访问
        const token = localStorage.getItem('auth_token');
        if (token) {
            console.log('API请求失败但有token，允许访问');
            next();
        } else {
            console.log('API请求失败且无token，跳转登录页');
            isRedirecting = true;
            window.isGlobalRedirecting = true;
            ElMessage.error('网络连接失败，请检查网络后重试');
            next('/login');
        }
    }
})

// 路由后置守卫，重置跳转标记
router.afterEach(() => {
    isRedirecting = false;
    window.isGlobalRedirecting = false;
})

// 暴露清除缓存函数，用于调试
window.clearIpWhitelistCache = () => {
    ipWhitelistCache = {
        result: null,
        timestamp: 0,
        error: null
    };
    console.log('IP白名单缓存已清除');
};

export default router
