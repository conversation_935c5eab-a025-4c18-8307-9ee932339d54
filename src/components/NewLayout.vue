<script setup>
import {computed, nextTick, onMounted, ref} from 'vue'
import {useRouter} from 'vue-router'
import {localAPI} from '../service/req'
import {ElMessage} from 'element-plus'
import NewChat from "./NewChat.vue";
import {Delete, Edit} from "@element-plus/icons-vue";

// 用户信息
const userInfo = ref(null)

// 初始化用户信息
const initUserInfo = () => {
  try {
    const storedUserInfo = localStorage.getItem('user_info')
    if (storedUserInfo) {
      userInfo.value = JSON.parse(storedUserInfo)
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
}

const router = useRouter()

const conversations = ref([])
const currentConversationId = ref(null)
const isNewConversation = ref(false)
const loading = ref(false)

// 白名单状态
const isWhiteListUser = ref(false)

// 检查是否为白名单用户
const checkWhiteListStatus = async () => {
  try {
    const response = await localAPI.isWhiteAuth()
    console.log('Layout白名单API响应:', response)

    // 安全地获取白名单状态
    let isAuth = false;
    // 由于响应拦截器返回的是response.data，所以这里直接使用response
    if (response) {
      // 根据你提供的数据结构：{code: 200, data: {isInWhitelist: true, ipAddress: "********"}, message: "IP白名单检查完成"}
      if (response.code === 200 && response.data && typeof response.data.isInWhitelist === 'boolean') {
        isAuth = response.data.isInWhitelist;
      } else if (response.data && typeof response.data.isInWhitelist === 'boolean') {
        isAuth = response.data.isInWhitelist;
      } else if (typeof response.isInWhitelist === 'boolean') {
        isAuth = response.isInWhitelist;
      }
    }

    isWhiteListUser.value = isAuth;
    console.log('Layout白名单状态:', isAuth);
  } catch (error) {
    console.error('检查白名单状态失败:', error)
    isWhiteListUser.value = false
  }
}

// 计算是否显示退出登录按钮
const showLogoutButton = computed(() => {
  // 如果有token但不在白名单中，说明是通过账号密码登录的，显示退出按钮
  const token = localStorage.getItem('auth_token');
  return token && !isWhiteListUser.value;
})

// 移动端状态管理
const isMobile = ref(false)
const showSidebar = ref(false)

// 检测移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
  if (!isMobile.value) {
    showSidebar.value = false
  }
}

// 切换侧边栏显示
const toggleSidebar = () => {
  showSidebar.value = !showSidebar.value
}

// 关闭侧边栏
const closeSidebar = () => {
  showSidebar.value = false
}

// 添加删除会话方法
const handleDeleteConversation = async (id) => {
  try {
    loading.value = true

    // 调用本地API删除会话
    await localAPI.deleteHistory(id)

    // 从本地列表中移除
    conversations.value = conversations.value.filter(conv => conv.chatId !== id)

    // 如果删除的是当前会话，切换到其他会话
    if (currentConversationId.value === id) {
      if (conversations.value.length > 0) {
        currentConversationId.value = conversations.value[0].chatId
        conversations.value[0].active = true
      } else {
        // 如果没有其他会话了，设置为新建会话状态
        currentConversationId.value = null
        isNewConversation.value = true
      }
    }

    ElMessage.success('删除成功')
  } catch (error) {
    console.error('删除会话失败:', error)
    ElMessage.error('删除会话失败')
  } finally {
    loading.value = false
  }
}

// 添加重命名会话方法
const startRenameConversation = (id) => {
  const conversation = conversations.value.find(conv => conv.chatId === id)
  if (conversation) {
    conversation.editing = true
    // 保存原标题，用于失败时恢复
    conversation.originalTitle = conversation.title
    // 延迟确保输入框已渲染
    nextTick(() => {
      const input = document.querySelector(`input[aria-labelledby="el-id-${conversation.chatId}-label"]`)
      if (input) {
        input.focus()
      }
    })
  }
}

const submitRename = async (conv) => {
  conv.editing = false
  try {
    loading.value = true
    // 调用本地API更新会话标题
    await localAPI.updateHistoryTitle(conv.chatId, conv.title)
    ElMessage.success('重命名成功')
  } catch (error) {
    console.error('重命名失败:', error)
    ElMessage.error('重命名失败')
    // 如果失败，恢复原标题
    conv.title = conv.originalTitle || conv.title
  } finally {
    loading.value = false
  }
}


const handleAddConversation = async () => {
  try {
    // 设置为新对话状态
    isNewConversation.value = true
    currentConversationId.value = null

    // 清除所有会话的active状态
    conversations.value.forEach(conv => {
      conv.active = false
    })
  } catch (error) {
    console.error('创建会话失败:', error)
    ElMessage.error('创建会话失败')
  } finally {
    loading.value = false
  }
}


const switchConversation = (id) => {
  isNewConversation.value = false
  currentConversationId.value = id
  conversations.value.forEach(conv => {
    conv.active = conv.chatId === id
  })

  // 移动端切换会话后关闭侧边栏
  if (isMobile.value) {
    closeSidebar()
  }
}

const createNewConversation = async (title) => {
  console.log('创建会话:', title)
  try {
    loading.value = true

    // 如果有当前会话ID，更新会话标题
    if (currentConversationId.value) {
      await localAPI.updateHistoryTitle(currentConversationId.value, title)

      // 更新本地会话标题
      const conversation = conversations.value.find(conv => conv.chatId === currentConversationId.value)
      if (conversation) {
        conversation.title = title
      }
    }

    isNewConversation.value = false
  } catch (error) {
    console.error('更新会话标题失败:', error)
    ElMessage.error('更新会话标题失败')
  } finally {
    loading.value = false
  }
}


// 加载会话列表
const loadConversations = async () => {
  try {
    loading.value = true
    const response = await localAPI.getChatHistories(0, 50)
    console.log('会话列表响应:', response)

    // 由于响应拦截器返回的是response.data，所以这里直接使用response
    if (response && response.data && response.data.list) {
      conversations.value = response.data.list.map(conv => ({
        id: conv.chatId,
        title: conv.customTitle || conv.title || `会话 ${conv.chatId.slice(-8)}`,
        active: false,
        creat_at: conv.updateTime,
        chatId: conv.chatId,
        top: conv.top || false
      }))

      // 按更新时间排序（最新的在前）
      conversations.value.sort((a, b) => {
        const timeA = typeof a.creat_at === 'number' ? a.creat_at : new Date(a.creat_at).getTime();
        const timeB = typeof b.creat_at === 'number' ? b.creat_at : new Date(b.creat_at).getTime();
        return timeB - timeA;
      })

      // 设置第一个会话为当前会话
      if (conversations.value.length > 0) {
        currentConversationId.value = conversations.value[0].chatId
        conversations.value[0].active = true
      }

      console.log('处理后的会话列表:', conversations.value)
    } else {
      console.log('会话列表数据格式不正确:', response)
      conversations.value = []
    }
  } catch (err) {
    console.error('加载会话列表失败：', err)
    ElMessage.error('加载会话列表失败')
    conversations.value = []
  } finally {
    loading.value = false
  }
}

// 组件挂载时加载会话列表
onMounted(() => {
  loadConversations()
  checkMobile()
  checkWhiteListStatus()
  initUserInfo()
  window.addEventListener('resize', checkMobile)
})

const setConversationId = (id) => {
  currentConversationId.value = id
  isNewConversation.value = false
  conversations.value.forEach(conv => {
    conv.active = conv.chatId === id
  })
}

// 获取当前会话标题
const getCurrentConversationTitle = () => {
  if (isNewConversation.value) {
    return '新会话'
  }
  const currentConv = conversations.value.find(conv => conv.chatId === currentConversationId.value)
  return currentConv ? currentConv.title : '智能回答'
}

// 格式化会话时间
const formatConversationTime = (timeStr) => {
  if (!timeStr) return ''

  try {
    const date = new Date(timeStr)
    const now = new Date()

    // 如果是今天
    if (date.toDateString() === now.toDateString()) {
      return date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    // 如果是昨天
    const yesterday = new Date(now)
    yesterday.setDate(yesterday.getDate() - 1)
    if (date.toDateString() === yesterday.toDateString()) {
      return '昨天 ' + date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    // 如果是今年
    if (date.getFullYear() === now.getFullYear()) {
      return date.toLocaleDateString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    // 其他情况显示完整日期
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return ''
  }
}

// 退出登录处理
const handleLogout = async () => {
  try {
    // 清除本地存储的登录信息
    localStorage.removeItem('auth_token')
    localStorage.removeItem('user_info')

    // 提示退出登录成功
    ElMessage.success('退出登录成功')

    // 跳转到登录页面
    router.push('/login')

  } catch (error) {
    // 用户取消退出
    console.log('用户取消退出登录')
  }
}
</script>

<template>
  <div class="container">
    <!-- 移动端遮罩层 -->
    <div
        v-if="isMobile && showSidebar"
        class="mobile-overlay"
        @click="closeSidebar"
    ></div>

    <!-- 移动端顶部导航栏 -->
    <div v-if="isMobile" class="mobile-header">
      <button class="mobile-menu-btn" @click="toggleSidebar">
        ☰
      </button>
      <span class="mobile-title">{{ getCurrentConversationTitle() }}</span>
      <div></div>
    </div>

    <div :class="{ 'show': isMobile && showSidebar }" class="sidebar">
      <!-- 顶部标题 -->
      <div class="sidebar-header">
        <div class="logo">
          <span>政策AI助手</span>
        </div>
        <button class="new-chat-btn" @click="handleAddConversation">
          <i class="icon-plus"></i>
          新建会话
        </button>
      </div>

      <!-- 最近会话 -->
      <div class="recent-conversations">
        <div v-if="conversations.length === 0" class="conversation-empty">
          当前没有会话
        </div>
        <div class="conversation-list">
          <div
              v-for="conversation in conversations"
              :key="conversation.chatId"
              :class="['conversation-item', { active: conversation.active }]"
              @click="switchConversation(conversation.chatId)"
          >
            <div class="conversation-content">
              <div class="conversation-title">
                <span v-if="!conversation.editing">{{ conversation.title }}</span>
                <el-input
                    v-else
                    v-model="conversation.title"
                    size="small"
                    @blur="submitRename(conversation)"
                    @keyup.enter="submitRename(conversation)"
                />
              </div>
              <div class="conversation-time">{{ formatConversationTime(conversation.creat_at) }}</div>
            </div>
            <div class="conversation-actions">
              <el-icon @click.stop="startRenameConversation(conversation.chatId)">
                <Edit/>
              </el-icon>
              <el-icon @click.stop="handleDeleteConversation(conversation.chatId)">
                <Delete/>
              </el-icon>
            </div>
          </div>
        </div>
      </div>

      <!-- 用户信息 -->
      <div class="user-profile">
        <div class="avatar">{{ userInfo?.name?.[0] || '用' }}</div>
        <div class="user-info">
          <div class="user-name">{{ userInfo?.name || '用户' }}</div>
          <div class="user-title">{{ userInfo?.userType ? '管理员' : '普通用户' || '访客' }}</div>
        </div>
        <button v-if="showLogoutButton" class="logout-btn" @click="handleLogout">
          退出登录
        </button>
      </div>
    </div>

    <div class="content">
      <NewChat
          :conversation-id="currentConversationId"
          @refresh-conversations="loadConversations"
          @set-conversation-id="setConversationId"
      />
    </div>
  </div>
</template>

<style scoped>
.container {
  display: flex;
  height: 100vh;
  background-color: #ffffff;
  overflow: hidden;
}

.policy-ai-container {
  display: flex;
  height: 100vh;
  background-color: #f5f5f5;
}

/* 左侧边栏样式 */
.sidebar {
  width: 320px;
  background: #1a2133;
  color: white;
  display: flex;
  flex-direction: column;
  padding: 20px;
  transition: all 0.3s ease;
}

/* 移动端遮罩层 */
.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  animation: fadeIn 0.3s ease forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

/* 移动端顶部导航栏样式 */
.mobile-header {
  display: none;
}

/* 响应式布局 - 参考 Element Plus 的断点 */
/* xs: <768px */
@media (max-width: 767px) {
  .container {
    flex-direction: column;
    position: relative;
  }

  .sidebar {
    position: fixed;
    top: 0;
    left: -280px;
    width: 280px;
    height: 100vh;
    z-index: 1000;
    transform: translateX(0);
    transition: transform 0.3s ease;
    padding: 15px;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
  }

  .sidebar.show {
    transform: translateX(280px);
  }

  .content {
    width: 100%;
    height: calc(100vh - 48px);
    position: relative;
    margin-top: 48px;
  }

  .mobile-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: #1a2133;
    color: white;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    height: 48px;
    box-sizing: border-box;
  }

  .mobile-menu-btn {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background 0.2s;
  }

  .mobile-menu-btn:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  .mobile-title {
    font-size: 16px;
    font-weight: 600;
  }

  .sidebar-header {
    margin-bottom: 20px;
  }

  .new-chat-btn {
    padding: 10px;
    font-size: 13px;
  }

  .conversation-item {
    padding: 10px;
  }

  .conversation-title {
    font-size: 13px;
  }

  .conversation-time {
    font-size: 10px;
  }

  .user-profile {
    padding: 12px;
  }

  .avatar {
    width: 32px;
    height: 32px;
  }

  .user-name {
    font-size: 13px;
  }

  .user-title {
    font-size: 11px;
  }
}

/* sm: 768px-991px */
@media (min-width: 768px) and (max-width: 991px) {
  .sidebar {
    width: 280px;
    padding: 18px;
  }

  .sidebar-header {
    margin-bottom: 25px;
  }

  .logo {
    font-size: 16px;
    margin-bottom: 18px;
  }

  .new-chat-btn {
    padding: 11px;
    font-size: 13px;
  }

  .conversation-item {
    padding: 11px;
  }

  .conversation-title {
    font-size: 13px;
  }

  .conversation-time {
    font-size: 10px;
  }
}

/* md: 992px-1199px */
@media (min-width: 992px) and (max-width: 1199px) {
  .sidebar {
    width: 300px;
    padding: 19px;
  }

  .logo {
    font-size: 17px;
    margin-bottom: 19px;
  }

  .new-chat-btn {
    padding: 11px;
    font-size: 13px;
  }
}

/* lg: 1200px-1919px */
@media (min-width: 1200px) and (max-width: 1919px) {
  .sidebar {
    width: 320px;
  }
}

/* xl: ≥1920px */
@media (min-width: 1920px) {
  .sidebar {
    width: 360px;
    padding: 24px;
  }

  .logo {
    font-size: 20px;
    margin-bottom: 24px;
  }

  .new-chat-btn {
    padding: 14px;
    font-size: 15px;
  }

  .conversation-item {
    padding: 14px;
  }

  .conversation-title {
    font-size: 15px;
  }

  .user-profile {
    padding: 18px;
  }

  .avatar {
    width: 44px;
    height: 44px;
  }

  .user-name {
    font-size: 15px;
  }
}

.sidebar-header {
  margin-bottom: 30px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
}

.new-chat-btn {
  width: 100%;
  background: #377CF2;
  background: linear-gradient(90deg, rgba(55, 124, 242, 1) 50%, rgba(32, 82, 218, 1) 100%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.recent-conversations {
  flex: 1;
  margin-bottom: 20px;
}

.tabs {
  display: flex;
  margin-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.tab {
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.tab.active {
  opacity: 1;
  border-bottom: 2px solid white;
}

.conversation-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 10px;
  color: #9ca3af;
}

.conversation-list {
  overflow-y: auto;
  max-height: calc(100vh - 300px);
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding-right: 4px;
}

/* 移动端滚动条样式 */
.conversation-list::-webkit-scrollbar {
  width: 4px;
}

.conversation-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

.conversation-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
}

.conversation-list::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* 桌面端滚动条样式 - 覆盖移动端样式 */
@media (min-width: 768px) {
  .conversation-list::-webkit-scrollbar {
    width: 6px;
  }

  .conversation-list::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }

  .conversation-list::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    transition: background 0.2s ease;
  }

  .conversation-list::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
  }
}

/* Firefox 滚动条样式 */
.conversation-list {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) rgba(255, 255, 255, 0.1);
}

.conversation-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  border-left: 4px solid #3b82f6;
}

.conversation-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.conversation-item.active {
  background: rgba(255, 255, 255, 0.15);
}

.conversation-content {
  flex: 1;
}

.conversation-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
  color: white;
}

.conversation-time {
  font-size: 11px;
  opacity: 0.6;
  color: #9ca3af;
}

.conversation-snippet {
  font-size: 12px;
  opacity: 0.8;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.conversation-time {
  font-size: 11px;
  opacity: 0.6;
}

.conversation-actions {
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.conversation-item:hover .conversation-actions {
  opacity: 1;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.avatar {
  width: 40px;
  height: 40px;
  background: #377CF2;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
}

.user-title {
  font-size: 12px;
  opacity: 0.8;
}

.logout-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 14px;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.logout-btn:hover {
  opacity: 1;
}

.content {
  flex: 1;
}

</style>
