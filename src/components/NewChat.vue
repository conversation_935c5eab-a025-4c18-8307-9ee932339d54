<script lang="ts" setup>
import {computed, ref, watch} from 'vue';
import {localAPI} from '../service/req';
import {ElMessage} from 'element-plus';
import NewChatList from './NewChatList.vue';
import {CaretRight} from '@element-plus/icons-vue';

const props = defineProps({
  conversationId: {
    type: String,
    default: null,
  },
});

const emit = defineEmits(['create-conversation', 'refresh-conversations', 'set-conversation-id']);

// 用户输入
const userInput = ref('');
const charCount = ref(0);
const deepThinking = ref(false);
const loading = ref(false);

// 聊天消息列表
const messages = ref([]);
const chatListRef = ref(null);

// 判断是否显示欢迎页面
const showWelcome = computed(() => {
  return messages.value.length === 0 && !props.conversationId;
});

// 更新字符计数
const updateCharCount = () => {
  charCount.value = userInput.value.length;
};

// 发送消息
const sendMessage = async () => {
  const message = userInput.value.trim();
  if (!message || loading.value) return;

  try {
    loading.value = true;

    // 添加用户消息到列表
    const userMessage = {
      id: `user-${Date.now()}`,
      obj: 'user',
      value: message,
      time: new Date().toISOString(),
    };
    messages.value.push(userMessage);

    // 清空输入
    userInput.value = '';
    updateCharCount();

    // 构建API消息
    const apiMessages = [{
      role: 'user',
      content: message,
    }];

    // 使用当前会话ID或生成新的
    const chatId = props.conversationId || (Date.now().toString() + Math.random().toString(36).substr(2, 9));

    console.log('发送请求:', {
      chatId,
      messages: apiMessages,
      isDeepThinking: deepThinking.value,
    });

    // 调用API发送消息
    const res = await localAPI.sendChatMessage(chatId, apiMessages, deepThinking.value);
    console.log('聊天API响应:', res);

    // 处理响应
    let aiResponse = '';
    let thoughtChainMessage = '';

    // 安全地获取AI响应内容
    if (res && res.aiResponse) {
      aiResponse = res.aiResponse
    } else {
      aiResponse = '抱歉，我无法生成回复。'
    }

    // 获取思考链消息
    if (res.thoughtChainMessage) {
      thoughtChainMessage = res.thoughtChainMessage;
    }

    // 添加AI回复到列表
    const aiMessage = {
      id: `ai-${Date.now()}`,
      obj: 'AI',
      value: aiResponse,
      thoughtChainMessage: thoughtChainMessage,
    };
    messages.value.push(aiMessage);

    // 如果是新会话，需要刷新会话列表并设置当前会话ID
    if (!props.conversationId) {
      // 触发父组件刷新会话列表
      emit('refresh-conversations');
      // 设置当前会话ID，这样后续消息会使用同一个会话
      emit('set-conversation-id', chatId);
    }

  } catch (err) {
    console.error('请求失败：', err);
    let errorMessage = '发送消息失败';

    if (err.response?.data?.message) {
      errorMessage = err.response.data.message;
    } else if (err.message) {
      errorMessage = err.message;
    }

    ElMessage.error(errorMessage);

    // 添加错误消息
    const errorMsg = {
      id: `error-${Date.now()}`,
      role: 'ai',
      content: errorMessage,
      time: new Date().toISOString(),
      isError: true,
    };
    messages.value.push(errorMsg);

  } finally {
    loading.value = false;
  }
};

// 生成消息标题
const generateMessageTitle = (userMessage) => {
  // 根据用户消息内容生成合适的标题
  if (userMessage.includes('税收') || userMessage.includes('税务')) {
    return '税收政策咨询';
  } else if (userMessage.includes('创业') || userMessage.includes('企业')) {
    return '创业扶持政策咨询';
  } else if (userMessage.includes('就业') || userMessage.includes('毕业生')) {
    return '就业政策咨询';
  } else if (userMessage.includes('社保') || userMessage.includes('保险')) {
    return '社会保险政策咨询';
  } else {
    return '政策咨询';
  }
};

// 加载会话消息
const loadConversationMessages = async (conversationId) => {
  if (!conversationId) {
    messages.value = [];
    return;
  }

  try {
    loading.value = true;
    const response = await localAPI.getChatRecords(conversationId, 0, 100);
    console.log('加载会话消息:', response);

    if (response && response.code === 200 && response.data) {
      messages.value = response.data;
    } else {
      messages.value = [];
    }
  } catch (error) {
    console.error('加载会话消息失败:', error);
    messages.value = [];
  } finally {
    loading.value = false;
  }
};

// 监听会话ID变化
watch(() => props.conversationId, (newId) => {
  if (newId) {
    loadConversationMessages(newId);
  } else {
    messages.value = [];
  }
}, {immediate: true});

// 快速问题点击处理
const handleQuickQuestion = (question) => {
  if (loading.value) return;
  userInput.value = question.title;
  updateCharCount();
  sendMessage();
};

const quickQuestions = ref([
  {
    title: '最新创业扶持政策有哪些?',
    description: '了解国家及地方政府对创业者的最新政策扶持',
  },
  {
    title: '如何申请小型微利企业认定?',
    description: '获取小型微利企业认定的条件和申请流程',
  },
  {
    title: '灵活就业人员社保政策是什么?',
    description: '了解自由职业者和灵活就业人员的社保缴纳规定',
  },
  {
    title: '应届毕业生有哪些就业补贴?',
    description: '查询高校毕业生就业创业支持政策和补贴信息',
  },
]);
</script>

<template>
  <div class="chat-container">
    <!-- 欢迎页面 -->
    <div v-if="showWelcome" class="main-content">
      <!-- 欢迎信息 -->
      <div class="welcome-section">
        <h1>欢迎使用政策AI助手</h1>
        <p>您的智能政策咨询专家</p>
      </div>

      <!-- AI助手图标 -->
      <div class="ai-assistant">
        <div class="ai-greeting">
          <h2>您好,我是政策AI助手</h2>
          <p>可以回答您关于政府政策的各类问题</p>
        </div>
      </div>

      <!-- 快速问题卡片 -->
      <div class="quick-questions">
        <div
            v-for="(question, index) in quickQuestions"
            :key="index"
            class="question-card"
            @click="handleQuickQuestion(question)"
        >
          <div class="card-content">
            <h3>{{ question.title }}</h3>
            <p>{{ question.description }}</p>
          </div>
        </div>
      </div>

      <!-- 深度思考开关 -->
      <div class="deep-thinking">
        <div class="toggle-container">
          <label class="toggle-switch">
            <input v-model="deepThinking" type="checkbox"/>
            <span class="toggle-slider"></span>
          </label>
          <span class="toggle-label">深度思考</span>
        </div>
<!--        <p class="toggle-description">开启后,系统将对复杂问题进行更深入的政策分析</p>-->
      </div>

      <!-- 输入框 -->
      <div class="input-section">
        <div class="input-container">
            <textarea
                v-model="userInput"
                maxlength="2000"
                placeholder="请输入您的问题,我将为您提供专业解答..."
                @input="updateCharCount"
                @keydown.enter.prevent="sendMessage"
            ></textarea>
          <div class="input-footer">
            <span class="char-count">{{ charCount }}/2000</span>
            <button
                :class="{ 'loading': loading }"
                :disabled="loading || !userInput.trim()"
                class="send-btn"
                @click="sendMessage"
            >
              <el-icon><CaretRight /></el-icon>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 聊天界面 -->
    <div v-else class="chat-layout">
      <!-- 聊天消息列表 -->
      <NewChatList
          ref="chatListRef"
          :loading="loading"
          :messages="messages"
      />

      <!-- 底部输入区域 -->
      <div class="bottom-input-section">
        <!-- 深度思考开关 -->
        <div class="deep-thinking-inline">
          <div class="toggle-container">
            <label class="toggle-switch">
              <input v-model="deepThinking" type="checkbox"/>
              <span class="toggle-slider"></span>
            </label>
            <span class="toggle-label">深度思考</span>
          </div>
<!--          <p class="toggle-description">开启后,系统将对复杂问题进行更深入的政策分析</p>-->
        </div>

        <!-- 输入框 -->
        <div class="input-container">
          <textarea
              v-model="userInput"
              maxlength="2000"
              placeholder="请输入您的问题,我将为您提供专业解答..."
              @input="updateCharCount"
              @keydown.enter.prevent="sendMessage"
          ></textarea>
          <div class="input-footer">
            <span class="char-count">{{ charCount }}/2000</span>
            <button
                :class="{ 'loading': loading }"
                :disabled="loading || !userInput.trim()"
                class="send-btn"
                @click="sendMessage"
            >
              <el-icon>
                <CaretRight/>
              </el-icon>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 聊天容器 */
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background: white;
}

/* 聊天布局 */
.chat-layout {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}

/* 主内容区域样式 */
.main-content {
  flex: 1;
  background: white;
  padding: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  overflow-y: auto;
}

.welcome-section {
  margin-bottom: 40px;
}

.welcome-section h1 {
  font-size: 28px;
  font-weight: 600;
  color: #1e3a8a;
  margin-bottom: 8px;
}

.welcome-section p {
  font-size: 16px;
  color: #6b7280;
}

.ai-assistant {
  margin-bottom: 50px;
}

.ai-avatar {
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
}

.ai-avatar i {
  font-size: 48px;
  color: white;
}

.ai-greeting h2 {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.ai-greeting p {
  font-size: 16px;
  color: #6b7280;
}

.quick-questions {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 40px;
  max-width: 800px;
  width: 100%;
}

.question-card {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  text-align: left;
  cursor: pointer;
  transition: all 0.3s ease;
}

.question-card:hover {
  background: #e2e8f0;
  border-color: #cbd5e1;
  transform: translateY(-2px);
}

.card-content h3 {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.card-content p {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
}

.deep-thinking {
  max-width: 800px;
  width: 100%;
  margin-bottom: 40px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
}

.toggle-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: #3b82f6;
}

input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

.toggle-label {
  font-size: 16px;
  font-weight: 500;
  color: #1f2937;
}

.toggle-description {
  font-size: 14px;
  color: #6b7280;
  text-align: center;
}

/* 底部输入区域 */
.bottom-input-section {
  padding: 20px;
  background: white;
  flex-shrink: 0;
}

.deep-thinking-inline {
  margin: 0 auto 16px auto;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
}

.input-section {
  width: 100%;
  max-width: 800px;
}

.bottom-input-section .input-container {
  margin: 0 auto;
}

.input-container {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  transition: border-color 0.3s ease;
}

.input-container:focus-within {
  border-color: #3b82f6;
}

.input-container textarea {
  width: 100%;
  min-height: 120px;
  border: none;
  outline: none;
  resize: none;
  font-size: 16px;
  line-height: 1.5;
  color: #1f2937;
  background: transparent;
}

.input-container textarea::placeholder {
  color: #9ca3af;
}

.input-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
}

.char-count {
  font-size: 14px;
  color: #6b7280;
}

.send-btn {
  width: 48px;
  height: 48px;
  background: #3b82f6;
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s ease;
}

.send-btn:hover {
  background: #2563eb;
}

.send-btn i {
  font-size: 18px;
}

.send-btn:disabled {
  background: #94a3b8;
  cursor: not-allowed;
}

.send-btn.loading {
  position: relative;
  overflow: hidden;
}

.send-btn.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 200%;
  height: 100%;
  background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
  );
  animation: loading 1.5s infinite;
}

@keyframes loading {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(100%);
  }
}

/* 响应式布局 - 参考 Element Plus 的断点 */
/* xs: <768px */
@media (max-width: 767px) {
  .main-content {
    padding: 20px 16px;
  }

  .welcome-section h1 {
    font-size: 24px;
  }

  .welcome-section p {
    font-size: 14px;
  }

  .ai-greeting h2 {
    font-size: 20px;
  }

  .ai-greeting p {
    font-size: 14px;
  }

  .quick-questions {
    grid-template-columns: 1fr;
    gap: 16px;
    margin-bottom: 30px;
  }

  .question-card {
    padding: 16px;
  }

  .card-content h3 {
    font-size: 14px;
  }

  .card-content p {
    font-size: 12px;
  }

  .deep-thinking {
    margin-bottom: 30px;
  }

  .toggle-label {
    font-size: 14px;
  }

  .toggle-description {
    font-size: 12px;
  }

  .input-container {
    max-width: 100%;
    padding: 16px;
  }

  .input-container textarea {
    min-height: 100px;
    font-size: 14px;
  }

  .send-btn {
    width: 40px;
    height: 40px;
  }

  .bottom-input-section {
    padding: 16px;
  }

  .deep-thinking-inline {
    max-width: 100%;
    margin-bottom: 12px;
  }
}

/* sm: 768px-991px */
@media (min-width: 768px) and (max-width: 991px) {
  .main-content {
    padding: 32px 24px;
  }

  .welcome-section h1 {
    font-size: 26px;
  }

  .ai-avatar {
    width: 100px;
    height: 100px;
  }

  .ai-avatar i {
    font-size: 40px;
  }

  .ai-greeting h2 {
    font-size: 22px;
  }

  .quick-questions {
    grid-template-columns: repeat(2, 1fr);
    gap: 18px;
    max-width: 700px;
  }

  .question-card {
    padding: 18px;
  }

  .input-container {
    max-width: 900px;
    padding: 18px;
  }

  .input-container textarea {
    min-height: 110px;
  }

  .send-btn {
    width: 44px;
    height: 44px;
  }

  .bottom-input-section {
    padding: 18px;
  }

  .deep-thinking-inline {
    max-width: 900px;
  }
}

/* md: 992px-1199px */
@media (min-width: 992px) and (max-width: 1199px) {
  .main-content {
    padding: 36px 28px;
  }

  .quick-questions {
    max-width: 750px;
  }

  .input-container {
    max-width: 1000px;
    padding: 19px;
  }

  .bottom-input-section {
    padding: 19px;
  }

  .deep-thinking-inline {
    max-width: 1000px;
  }
}

/* lg: 1200px-1919px */
@media (min-width: 1200px) and (max-width: 1919px) {
  .main-content {
    padding: 40px;
  }

  .quick-questions {
    max-width: 800px;
  }
  .input-container {
    max-width: 1200px;
    padding: 20px;
  }

  .deep-thinking-inline {
    max-width: 1200px;
  }
}

/* xl: ≥1920px */
@media (min-width: 1920px) {
  .main-content {
    padding: 48px;
  }

  .welcome-section h1 {
    font-size: 32px;
    margin-bottom: 12px;
  }

  .welcome-section p {
    font-size: 18px;
  }

  .ai-avatar {
    width: 140px;
    height: 140px;
    margin-bottom: 24px;
  }

  .ai-avatar i {
    font-size: 56px;
  }

  .ai-greeting h2 {
    font-size: 28px;
    margin-bottom: 12px;
  }

  .ai-greeting p {
    font-size: 18px;
  }

  .quick-questions {
    gap: 24px;
    margin-bottom: 48px;
    max-width: 1000px;
  }

  .question-card {
    padding: 24px;
  }

  .card-content h3 {
    font-size: 18px;
    margin-bottom: 12px;
  }

  .card-content p {
    font-size: 16px;
  }

  .deep-thinking {
    margin-bottom: 20px;
  }

  .toggle-label {
    font-size: 18px;
  }

  .toggle-description {
    font-size: 16px;
  }

  .input-container {
    max-width: 1400px;
    padding: 24px;
  }

  .input-container textarea {
    min-height: 140px;
    font-size: 18px;
  }

  .send-btn {
    width: 56px;
    height: 56px;
  }

  .char-count {
    font-size: 16px;
  }

  .bottom-input-section {
    padding: 24px;
  }

  .deep-thinking-inline {
    max-width: 1400px;
    margin-bottom: 20px;
  }
}
</style>
