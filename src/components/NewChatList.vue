<template>
  <div class="chat-list-container">
    <div ref="messagesContainer" class="chat-messages">
      <div
          v-for="message in messages"
          :key="message.id"
          class="message-item"
      >
        <!-- AI消息 -->
        <div v-if="message.obj === 'AI'" class="ai-message">
          <div class="ai-avatar">
            <span>AI</span>
          </div>
          <div class="message-content-ai">
            <div class="message-header">
            </div>
            <div class="message-text" v-html="formatContent(message.value)"></div>
            <!-- 深度思考显示 -->
            <div class="thought-chain" v-if="message.thoughtChainMessage !== '非深度思考'">
              <div class="thought-header" @click="toggleThought(message)" style="cursor: pointer; display: flex; align-items: center;">
                <span style="font-weight: bold; margin-right: 6px;">
                  {{ message._thoughtCollapsed ? '展开思考链' : '收起思考链' }}
                </span>
                <span :style="{transform: message._thoughtCollapsed ? 'rotate(0deg)' : 'rotate(90deg)', transition: 'transform 0.2s'}">▶</span>
              </div>
              <div v-show="!message._thoughtCollapsed" class="thought-content">
                <p v-html="formatContent(message.thoughtChainMessage)"></p>
              </div>
            </div>
          </div>
        </div>

        <!-- 用户消息 -->
        <div v-else class="user-message">
          <div class="message-content-user">
            <div class="message-text" v-html="formatContent(message.value)"></div>
          </div>
          <div class="user-avatar">
            <span>hu</span>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-message">
        <div class="ai-avatar">
          <span>AI</span>
        </div>
        <div class="message-content-ai">
          <div class="loading-container">
            <div class="loading-text">AI思考中，请稍等...</div>
            <div class="loading-dots">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {nextTick, ref, watch} from 'vue'
import {ElMessage} from 'element-plus'

const props = defineProps({
  messages: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

/**
 * 格式化消息内容，支持简单的Markdown语法
 * @param {string} content - 原始内容
 * @returns {string} 格式化后的HTML内容
 */
const formatContent = (content) => {
  if (!content) return ''
  return content
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/`(.*?)`/g, '<code>$1</code>')
      .replace(/\n/g, '<br>')
}

const messagesContainer = ref(null)

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}

// 监听消息变化，自动滚动到底部
watch(() => props.messages, (newMessages) => {
  if (!newMessages) return
  scrollToBottom()
}, {deep: true})

watch(() => props.loading, () => {
  scrollToBottom()
})

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return ''

  try {
    const date = new Date(timeStr)
    const now = new Date()

    // 如果是今天
    if (date.toDateString() === now.toDateString()) {
      return date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    // 如果是今年
    if (date.getFullYear() === now.getFullYear()) {
      return date.toLocaleDateString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    // 其他情况显示完整日期
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return ''
  }
}
// 切换思考链显示状态
const toggleThought = (message) => {
  // 使用 Vue 的响应式系统来切换折叠状态
  if (message._thoughtCollapsed === undefined) {
    // 如果还没有设置过，默认展开
    message._thoughtCollapsed = false
  }
  message._thoughtCollapsed = !message._thoughtCollapsed
}

// 复制消息内容
const copyMessage = async (content) => {
  try {
    await navigator.clipboard.writeText(content)
    ElMessage.success('已复制到剪贴板')
  } catch (error) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = content
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    ElMessage.success('已复制到剪贴板')
  }
}

// 暴露方法给父组件
defineExpose({
  scrollToBottom
})
</script>

<style scoped>
.chat-list-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.chat-messages {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
}

.message-item {
  display: flex;
  width: 100%;
  max-width: 1200px;
}

/* AI消息样式 */
.ai-message {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  max-width: 85%;
}

.ai-avatar {
  width: 40px;
  height: 40px;
  background: #3b82f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  color: white;
}

.message-content-ai {
  flex: 1;
  background: #f8fafc;
  padding: 16px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
}

.message-content-user {
  padding: 16px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

.message-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  flex-wrap: wrap;
  gap: 8px;
}

.message-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.message-time {
  font-size: 12px;
  color: #6b7280;
}

.message-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #e5e7eb;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.action-btn:hover {
  background: #d1d5db;
}

.action-btn i {
  font-size: 14px;
  color: #6b7280;
}

.message-text {
  color: #374151;
  line-height: 1.6;
  font-size: 14px;
}

.message-text :deep(strong) {
  font-weight: 600;
  color: #1f2937;
}

.message-text :deep(br) {
  margin-bottom: 8px;
}

/* 深度思考样式 */
.thought-chain {
  margin-top: 16px;
  padding: 12px;
  background: #eff6ff;
}

.thought-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #1e40af;
}

.thought-description {
  color: #6b7280;
  font-size: 12px;
  margin-left: auto;
}

/* 用户消息样式 */
.user-message {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  justify-content: flex-end;
  max-width: 85%;
  margin-left: auto;
}


.user-message .message-text {
  color: black;
}

.user-avatar {
  width: 40px;
  height: 40px;
  background: #3b82f6;
  border-radius: 50%;
  display: flex;
  color: white;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  order: 2;
}

/* 加载状态 */
.loading-message {
  display: flex;
  width: 100%;
  max-width: 1200px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 20px;
}

.loading-text {
  font-size: 16px;
  font-weight: 500;
  color: #1e40af;
  text-align: center;
  letter-spacing: 1px;
  background: linear-gradient(45deg, #1e40af, #3b82f6);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: text-glow 2s ease-in-out infinite alternate;
}

.loading-dots {
  display: flex;
  gap: 8px;
  align-items: center;
  padding: 8px 0;
}

.loading-dots span {
  width: 12px;
  height: 12px;
  background: linear-gradient(45deg, #3b82f6, #1e40af);
  border-radius: 50%;
  animation: loading-bounce 1.4s infinite ease-in-out both;
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
}

.loading-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes loading-bounce {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.3;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes text-glow {
  0% {
    filter: drop-shadow(0 0 5px rgba(30, 64, 175, 0.3));
  }
  100% {
    filter: drop-shadow(0 0 15px rgba(59, 130, 246, 0.6));
  }
}

/* 滚动条样式 */
.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}


/* 响应式设计 - 参考 Element Plus 的断点 */
/* xs: <768px */
@media (max-width: 767px) {
  .chat-messages {
    padding: 12px;
    gap: 16px;
    align-items: stretch;
  }

  .loading-message {
    max-width: 100%;
  }

  .message-item {
    max-width: 100%;
  }

  .ai-message,
  .user-message {
    max-width: 100%;
  }

  .ai-avatar,
  .user-avatar {
    width: 32px;
    height: 32px;
    font-size: 12px;
  }

  .message-content-ai,
  .message-content-user {
    padding: 12px;
    font-size: 13px;
  }

  .message-text {
    font-size: 13px;
    line-height: 1.5;
  }

  .thought-chain {
    margin-top: 12px;
    padding: 10px;
  }

  .thought-header {
    font-size: 12px;
  }

  .loading-message {
    max-width: 100%;
  }

  .loading-container {
    padding: 16px;
    gap: 10px;
  }

  .loading-text {
    font-size: 14px;
    letter-spacing: 0.5px;
  }

  .loading-dots {
    padding: 6px 0;
    gap: 6px;
  }

  .loading-dots span {
    width: 10px;
    height: 10px;
  }
}

/* sm: 768px-991px */
@media (min-width: 768px) and (max-width: 991px) {
  .chat-messages {
    padding: 16px;
    gap: 20px;
  }

  .loading-message {
    max-width: 900px;
  }

  .loading-container {
    padding: 18px;
    gap: 11px;
  }

  .loading-text {
    font-size: 15px;
  }

  .loading-dots {
    padding: 7px 0;
    gap: 7px;
  }

  .loading-dots span {
    width: 11px;
    height: 11px;
  }

  .message-item {
    max-width: 900px;
  }

  .ai-message,
  .user-message {
    max-width: 90%;
  }

  .ai-avatar,
  .user-avatar {
    width: 36px;
    height: 36px;
  }

  .message-content-ai,
  .message-content-user {
    padding: 14px;
  }

  .message-text {
    font-size: 13px;
  }

  .thought-chain {
    margin-top: 14px;
    padding: 11px;
  }

  .thought-header {
    font-size: 13px;
  }
}

/* md: 992px-1199px */
@media (min-width: 992px) and (max-width: 1199px) {
  .chat-messages {
    padding: 18px;
    gap: 22px;
  }

  .loading-message {
    max-width: 1000px;
  }

  .loading-container {
    padding: 19px;
    gap: 12px;
  }

  .loading-text {
    font-size: 16px;
  }

  .loading-dots {
    padding: 8px 0;
    gap: 8px;
  }

  .loading-dots span {
    width: 12px;
    height: 12px;
  }

  .message-item {
    max-width: 1000px;
  }

  .ai-message,
  .user-message {
    max-width: 87%;
  }

  .ai-avatar,
  .user-avatar {
    width: 38px;
    height: 38px;
  }

  .message-content-ai,
  .message-content-user {
    padding: 15px;
  }

  .thought-chain {
    margin-top: 15px;
    padding: 11px;
  }
}

/* lg: 1200px-1919px */
@media (min-width: 1200px) and (max-width: 1919px) {
  .chat-messages {
    padding: 20px;
    gap: 24px;
  }

  .loading-message {
    max-width: 1200px;
  }

  .loading-container {
    padding: 20px;
    gap: 12px;
  }

  .loading-text {
    font-size: 16px;
  }

  .loading-dots {
    padding: 8px 0;
    gap: 8px;
  }

  .loading-dots span {
    width: 12px;
    height: 12px;
  }

  .message-item {
    max-width: 1200px;
  }

  .ai-message,
  .user-message {
    max-width: 85%;
  }
}

/* xl: ≥1920px */
@media (min-width: 1920px) {
  .chat-messages {
    padding: 24px;
    gap: 28px;
  }

  .loading-message {
    max-width: 1400px;
  }

  .loading-container {
    padding: 24px;
    gap: 14px;
  }

  .loading-text {
    font-size: 18px;
    letter-spacing: 1.5px;
  }

  .loading-dots {
    padding: 10px 0;
    gap: 10px;
  }

  .loading-dots span {
    width: 14px;
    height: 14px;
  }

  .message-item {
    max-width: 1400px;
  }

  .ai-message,
  .user-message {
    max-width: 80%;
  }

  .ai-avatar,
  .user-avatar {
    width: 48px;
    height: 48px;
  }

  .message-content-ai,
  .message-content-user {
    padding: 20px;
  }

  .message-text {
    font-size: 16px;
    line-height: 1.7;
  }

  .thought-chain {
    margin-top: 20px;
    padding: 16px;
  }

  .thought-header {
    font-size: 16px;
  }
}
</style>
