<script setup>
import {computed, nextTick, onMounted, ref} from 'vue'
import {useRouter} from 'vue-router'
import {ChatSquare, Delete, Edit, MoreFilled, Plus, ArrowLeft, Menu, SwitchButton} from '@element-plus/icons-vue'
import Chat from "./chat.vue"
import {localAPI} from '../service/req'
import {ElMessage, ElMessageBox} from 'element-plus'
import { STORAGE_KEYS } from '../config/api.js'

const router = useRouter()

const conversations = ref([])
const currentConversationId = ref(null)
const isNewConversation = ref(false)
const loading = ref(false)

// 白名单状态
const isWhiteListUser = ref(false)

// 检查是否为白名单用户
const checkWhiteListStatus = async () => {
  try {
    const response = await localAPI.isWhiteAuth()
    console.log('Layout白名单API响应:', response)
    
    // 安全地获取白名单状态
    let isAuth = false;
    // 由于响应拦截器返回的是response.data，所以这里直接使用response
    if (response) {
      // 根据你提供的数据结构：{code: 200, data: {isInWhitelist: true, ipAddress: "********"}, message: "IP白名单检查完成"}
      if (response.code === 200 && response.data && typeof response.data.isInWhitelist === 'boolean') {
        isAuth = response.data.isInWhitelist;
      } else if (response.data && typeof response.data.isInWhitelist === 'boolean') {
        isAuth = response.data.isInWhitelist;
      } else if (typeof response.isInWhitelist === 'boolean') {
        isAuth = response.isInWhitelist;
      }
    }
    
    isWhiteListUser.value = isAuth;
    console.log('Layout白名单状态:', isAuth);
  } catch (error) {
    console.error('检查白名单状态失败:', error)
    isWhiteListUser.value = false
  }
}

// 计算是否显示退出登录按钮
const showLogoutButton = computed(() => {
  // 如果有token但不在白名单中，说明是通过账号密码登录的，显示退出按钮
  const token = localStorage.getItem('auth_token');
  return token && !isWhiteListUser.value;
})

// 移动端状态管理
const isMobile = ref(false)
const showSidebar = ref(false)

// 检测移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
  if (!isMobile.value) {
    showSidebar.value = false
  }
}

// 切换侧边栏显示
const toggleSidebar = () => {
  showSidebar.value = !showSidebar.value
}

// 关闭侧边栏
const closeSidebar = () => {
  showSidebar.value = false
}

// 添加删除会话方法
const handleDeleteConversation = async (id) => {
  try {
    loading.value = true
    
    // 调用本地API删除会话
    await localAPI.deleteHistory(id)
    
    // 从本地列表中移除
    conversations.value = conversations.value.filter(conv => conv.chatId !== id)
    
    // 如果删除的是当前会话，切换到其他会话
    if (currentConversationId.value === id) {
      if (conversations.value.length > 0) {
        currentConversationId.value = conversations.value[0].chatId
        conversations.value[0].active = true
      } else {
        // 如果没有其他会话了，设置为新建会话状态
        currentConversationId.value = null
        isNewConversation.value = true
      }
    }
    
    ElMessage.success('删除成功')
  } catch (error) {
    console.error('删除会话失败:', error)
    ElMessage.error('删除会话失败')
  } finally {
    loading.value = false
  }
}

// 添加重命名会话方法
const startRenameConversation = (id) => {
  const conversation = conversations.value.find(conv => conv.chatId === id)
  if (conversation) {
    conversation.editing = true
    // 保存原标题，用于失败时恢复
    conversation.originalTitle = conversation.title
    // 延迟确保输入框已渲染
    nextTick(() => {
      const input = document.querySelector(`input[aria-labelledby="el-id-${conversation.chatId}-label"]`)
      if (input) {
        input.focus()
      }
    })
  }
}

const submitRename = async (conv) => {
  conv.editing = false
  try {
    loading.value = true
    // 调用本地API更新会话标题
    await localAPI.updateHistoryTitle(conv.chatId, conv.title)
    ElMessage.success('重命名成功')
  } catch (error) {
    console.error('重命名失败:', error)
    ElMessage.error('重命名失败')
    // 如果失败，恢复原标题
    conv.title = conv.originalTitle || conv.title
  } finally {
    loading.value = false
  }
}


const handleAddConversation = async () => {
  try {
    // 设置为新对话状态
    isNewConversation.value = true
    currentConversationId.value = null
    
    // 清除所有会话的active状态
    conversations.value.forEach(conv => {
      conv.active = false
    })
  } catch (error) {
    console.error('创建会话失败:', error)
    ElMessage.error('创建会话失败')
  } finally {
    loading.value = false
  }
}


const switchConversation = (id) => {
  isNewConversation.value = false
  currentConversationId.value = id
  conversations.value.forEach(conv => {
    conv.active = conv.chatId === id
  })

  // 移动端切换会话后关闭侧边栏
  if (isMobile.value) {
    closeSidebar()
  }
}

const createNewConversation = async (title) => {
  console.log('创建会话:', title)
  try {
    loading.value = true
    
    // 如果有当前会话ID，更新会话标题
    if (currentConversationId.value) {
      await localAPI.updateHistoryTitle(currentConversationId.value, title)
      
      // 更新本地会话标题
      const conversation = conversations.value.find(conv => conv.chatId === currentConversationId.value)
      if (conversation) {
        conversation.title = title
      }
    }

    isNewConversation.value = false
  } catch (error) {
    console.error('更新会话标题失败:', error)
    ElMessage.error('更新会话标题失败')
  } finally {
    loading.value = false
  }
}

// 时间格式化函数
const formatDate = (dateStr) => {
  if (!dateStr) {
    // 如果时间字段不存在，使用当前时间
    const now = new Date();
    return {
      year: now.getFullYear(),
      month: now.getMonth(),
      date: now.getDate(),
      timestamp: now.getTime()
    };
  }

  // 处理时间戳或日期字符串
  let date;
  if (typeof dateStr === 'number') {
    // 如果是时间戳，直接创建Date对象
    date = new Date(dateStr);
  } else {
    // 如果是字符串，尝试解析
    date = new Date(dateStr);
  }

  // 验证日期是否有效
  if (isNaN(date.getTime())) {
    // 无效日期，使用当前时间
    const now = new Date();
    return {
      year: now.getFullYear(),
      month: now.getMonth(),
      date: now.getDate(),
      timestamp: now.getTime()
    };
  }

  return {
    year: date.getFullYear(),
    month: date.getMonth(),
    date: date.getDate(),
    timestamp: date.getTime()
  };
};

// 添加时间分组计算属性
const groupedConversations = computed(() => {
  const groups = {};
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime();
  const yesterday = today - 24 * 60 * 60 * 1000;

  conversations.value.forEach(conv => {
    // 兼容两种可能的时间字段
    const timeField = conv.creat_at || conv.createTime;
    const convTime = formatDate(timeField);
    let groupKey;

    // 判断时间分组
    if (convTime.timestamp >= today) {
      groupKey = '今天';
    } else if (convTime.timestamp >= yesterday) {
      groupKey = '昨天';
    } else if (convTime.year === now.getFullYear()) {
      // 同年显示月份
      groupKey = `${convTime.month + 1}月`;
    } else {
      // 不同年显示年月
      groupKey = `${convTime.year}年${convTime.month + 1}月`;
    }

    if (!groups[groupKey]) {
      groups[groupKey] = [];
    }
    groups[groupKey].push(conv);
  });

  // 对分组进行排序
  const sortOrder = {
    '今天': 0,
    '昨天': 1
  };

  return Object.entries(groups)
      .sort(([keyA], [keyB]) => {
        // 今天和昨天特殊处理
        if (sortOrder[keyA] !== undefined && sortOrder[keyB] !== undefined) {
          return sortOrder[keyA] - sortOrder[keyB];
        }
        if (sortOrder[keyA] !== undefined) return -1;
        if (sortOrder[keyB] !== undefined) return 1;

        // 其他按时间倒序
        return keyB.localeCompare(keyA);
      })
      .map(([key, items]) => ({
        title: key,
        items: items.sort((a, b) => {
          // 安全地处理时间排序
          const timeA = typeof a.creat_at === 'number' ? a.creat_at : new Date(a.creat_at).getTime();
          const timeB = typeof b.creat_at === 'number' ? b.creat_at : new Date(b.creat_at).getTime();
          return timeB - timeA;
        })
      }));
});

// 加载会话列表
const loadConversations = async () => {
  try {
    loading.value = true
    const response = await localAPI.getChatHistories(0, 50)
    console.log('会话列表响应:', response)
    
    // 由于响应拦截器返回的是response.data，所以这里直接使用response
    if (response && response.data && response.data.list) {
      conversations.value = response.data.list.map(conv => ({
        id: conv.chatId,
        title: conv.customTitle || conv.title || `会话 ${conv.chatId.slice(-8)}`,
        active: false,
        creat_at: conv.updateTime,
        chatId: conv.chatId,
        top: conv.top || false
      }))
      
      // 按更新时间排序（最新的在前）
      conversations.value.sort((a, b) => {
        const timeA = typeof a.creat_at === 'number' ? a.creat_at : new Date(a.creat_at).getTime();
        const timeB = typeof b.creat_at === 'number' ? b.creat_at : new Date(b.creat_at).getTime();
        return timeB - timeA;
      })
      
      // 设置第一个会话为当前会话
      if (conversations.value.length > 0) {
        currentConversationId.value = conversations.value[0].chatId
        conversations.value[0].active = true
      }
      
      console.log('处理后的会话列表:', conversations.value)
    } else {
      console.log('会话列表数据格式不正确:', response)
      conversations.value = []
    }
  } catch (err) {
    console.error('加载会话列表失败：', err)
    ElMessage.error('加载会话列表失败')
    conversations.value = []
  } finally {
    loading.value = false
  }
}

// 组件挂载时加载会话列表
onMounted(() => {
  loadConversations()
  checkMobile()
  checkWhiteListStatus()
  window.addEventListener('resize', checkMobile)
})

const setConversationId = (id) => {
  currentConversationId.value = id
  isNewConversation.value = false
  conversations.value.forEach(conv => {
    conv.active = conv.chatId === id
  })
}

// 获取当前会话标题
const getCurrentConversationTitle = () => {
  if (isNewConversation.value) {
    return '新会话'
  }
  const currentConv = conversations.value.find(conv => conv.chatId === currentConversationId.value)
  return currentConv ? currentConv.title : '智能回答'
}

// 退出登录处理
const handleLogout = async () => {
  try {
    // 清除本地存储的登录信息
    localStorage.removeItem('auth_token')
    localStorage.removeItem('user_info')

    // 提示退出登录成功
    ElMessage.success('退出登录成功')
    
    // 跳转到登录页面
    router.push('/login')
    
  } catch (error) {
    // 用户取消退出
    console.log('用户取消退出登录')
  }
}
</script>

<template>
  <div class="container" :class="{ 'mobile': isMobile }">
    <!-- 移动端顶部导航栏 -->
    <div v-if="isMobile" class="mobile-header">
      <div class="mobile-header-left">
        <el-button
          v-if="currentConversationId || isNewConversation"
          type="text"
          @click="toggleSidebar"
          class="menu-btn"
        >
          <el-icon><Menu /></el-icon>
        </el-button>
        <span v-else class="mobile-title">智能回答</span>
      </div>
      <div class="mobile-header-center">
        <span v-if="currentConversationId || isNewConversation" class="mobile-title">
          {{ getCurrentConversationTitle() }}
        </span>
      </div>
      <div class="mobile-header-right">
        <el-button
          v-if="currentConversationId || isNewConversation"
          type="text"
          @click="handleAddConversation"
          class="new-chat-btn-mobile"
        >
          <el-icon><Plus /></el-icon>
        </el-button>
      </div>
    </div>

    <!-- 侧边栏 -->
    <div
      class="left-menu"
      :class="{
        'mobile-sidebar': isMobile,
        'sidebar-open': showSidebar
      }"
    >
      <div class="logo">
        <img
            src="https://img.freepik.com/free-vector/chatbot-chat-message-vectorart_78370-4104.jpg?semt=ais_hybrid&w=740"
            alt="Logo" class="logo-img"/>
        <span class="logo-text">智能回答</span>
        <!-- 移动端关闭按钮 -->
        <el-button
          v-if="isMobile"
          type="text"
          @click="closeSidebar"
          class="close-sidebar-btn"
        >
          <el-icon><ArrowLeft /></el-icon>
        </el-button>
      </div>

      <div class="new-chat">
        <el-button type="primary" class="new-chat-btn" @click="handleAddConversation">
          <el-icon>
            <Plus/>
          </el-icon>
          <span>新建会话</span>
        </el-button>
      </div>

      <div class="conversation-list">
        <el-empty v-if="!loading && conversations.length === 0" description="暂无会话"/>
        <el-skeleton v-if="loading" :rows="3" animated/>

        <div v-if="!loading && conversations.length > 0" class="conversation-group">
          <div v-for="group in groupedConversations" :key="group.title" class="time-group">
            <div class="time-divider">{{ group.title }}</div>
            <div
                v-for="conv in group.items"
                :key="conv.chatId"
                class="conversation-item"
                :class="{ active: conv.active }"
                @click="switchConversation(conv.chatId)"
            >
              <div class="message_container">
                <div class="left">
                  <el-icon>
                    <ChatSquare/>
                  </el-icon>
                  <span v-if="!conv.editing" class="conversation-title">{{ conv.title }}</span>
                  <el-input 
                    v-else
                    ref="titleInput"
                    v-model="conv.title" 
                    @keyup.enter="submitRename(conv)"
                    @blur="submitRename(conv)"
                    size="small"
                  ></el-input>
                </div>
                <div class="right">
                  <el-dropdown trigger="click" @command="(command) => command.handler(conv.chatId)">
                    <el-icon @click.stop>
                      <MoreFilled/>
                    </el-icon>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item :command="{ handler: (id) => startRenameConversation(id) }">
                          <el-icon><Edit/></el-icon>
                          <span>重命名</span>
                        </el-dropdown-item>
                        <el-dropdown-item
                            :command="{ handler: handleDeleteConversation }"
                            class="delete-action"
                        >
                          <el-icon><Delete/></el-icon>
                          <span>删除</span>
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 退出登录区域 -->
        <div v-if="showLogoutButton" class="logout-section">
          <el-button 
            type="danger" 
            class="logout-button" 
            @click="handleLogout"
            :icon="SwitchButton"
          >
            退出登录
          </el-button>
        </div>
      </div>
    </div>

    <!-- 移动端遮罩层 -->
    <div
      v-if="isMobile && showSidebar"
      class="mobile-overlay"
      @click="closeSidebar"
    ></div>

    <div class="content" :class="{ 'mobile-content': isMobile }">
      <Chat
          class="chat-container"
          :conversation-id="currentConversationId"
          :is-new-conversation="isNewConversation || conversations.length === 0"
          @create-conversation="createNewConversation"
          @refresh-conversations="loadConversations"
          @set-conversation-id="setConversationId"
      />
    </div>
  </div>
</template>

<style scoped>
.container {
  display: flex;
  height: 100vh;
  background-color: #ffffff;
  overflow: hidden;
}

.container.mobile {
  flex-direction: column;
}

/* 移动端顶部导航栏 */
.mobile-header {
  height: 50px;
  background-color: #ffffff;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
  position: sticky;
  top: 0;
  z-index: 100;
  flex-shrink: 0;
}

.mobile-header-left,
.mobile-header-right {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.mobile-header-center {
  flex: 1;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.mobile-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.menu-btn,
.new-chat-btn-mobile {
  padding: 8px;
  color: #1a1a1a;
  font-size: 20px;
}

/* 侧边栏 */
.left-menu {
  width: 260px;
  background-color: #ffffff;
  color: #1a1a1a;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #e5e7eb;
  flex-shrink: 0;
}

/* 移动端侧边栏 */
.mobile-sidebar {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  z-index: 1000;
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  visibility: hidden;
}

.mobile-sidebar.sidebar-open {
  transform: translateX(0);
  visibility: visible;
}

/* 移动端遮罩层 */
.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

.logo {
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 10px;
  border-bottom: 1px solid #e5e7eb;
  position: relative;
  flex-shrink: 0;
}

.logo-img {
  width: 32px;
  height: 32px;
}

.logo-text {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  flex: 1;
}

.close-sidebar-btn {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
  font-size: 20px;
}

.new-chat {
  padding: 20px;
}

.new-chat-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  background-color: #0066ff;
  border: none;
  color: white;
}

.new-chat-btn:hover {
  background-color: #0052cc;
}

.conversation-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.conversation-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.conversation-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  border-radius: 6px;
  cursor: pointer;
  color: #1a1a1a;
}

.conversation-item:hover {
  background-color: #f3f4f6;
}

.conversation-item.active {
  background-color: #e6f0ff;
  color: #0066ff;
}

.conversation-title {
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
  display: inline-block;
}

.content {
  flex: 1;
  background-color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.mobile-content {
  height: calc(100vh - 60px);
  margin-top: 60px;
}

.message_container {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.message_container .left {
  gap: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.message_container .right {
  gap: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.el-dropdown-menu__item {
  display: flex;
  align-items: center;
  gap: 8px;
}

:deep(.delete-action) {
  color: #ff4d4f !important;
}

:deep(.delete-action:hover) {
  background-color: #fff1f0 !important;
}

:deep(.delete-action .el-icon) {
  color: #ff4d4f !important;
}

.el-icon {
  cursor: pointer;
}

.time-group {
  margin-bottom: 16px;
}

.time-divider {
  font-size: 12px;
  color: #000000;
  padding: 8px 16px;
  background-color: #fafafa;
  border-radius: 4px;
  margin-bottom: 8px;
}

/* 移动端响应式样式 */
@media (max-width: 768px) {
  .left-menu {
    width: 100%;
    max-width: 280px;
  }
  
  .conversation-title {
    max-width: 120px;
  }
  
  .logo {
    padding: 16px;
  }
  
  .new-chat {
    padding: 16px;
  }
  
  .conversation-list {
    padding: 8px;
  }
  
  .conversation-item {
    padding: 12px 8px;
  }
  
  .mobile-header {
    display: flex;
  }
  
  .content {
    margin-top: 0;
  }
}

/* 桌面端隐藏移动端元素 */
@media (min-width: 769px) {
  .mobile-header {
    display: none;
  }
  
  .mobile-sidebar {
    position: static;
    transform: translateX(0);
    transition: none;
    box-shadow: none;
    visibility: visible;
  }
  
  .mobile-overlay {
    display: none;
  }
}

/* 退出登录区域样式 */
.logout-section {
  padding: 20px;
  border-top: 1px solid #e5e7eb;
  flex-shrink: 0;
}

.logout-button {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  background-color: #ff4d4f;
  border: none;
  color: white;
  transition: all 0.3s ease;
}

.logout-button:hover {
  background-color: #ff7875;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 77, 79, 0.3);
}

.logout-button:active {
  transform: translateY(0);
}

/* 退出登录确认对话框样式 */
:deep(.logout-confirm-dialog) {
  .el-message-box__header {
    background-color: #fff5f5;
    border-bottom: 1px solid #fed7d7;
  }
  
  .el-message-box__title {
    color: #e53e3e;
    font-weight: 600;
  }
  
  .el-message-box__content {
    padding: 20px;
    color: #2d3748;
  }
  
  .el-message-box__btns {
    padding: 10px 20px 20px;
  }
}

/* 确保按钮样式正确显示 */
:deep(.el-button--danger) {
  background-color: #e53e3e !important;
  border-color: #e53e3e !important;
  color: white !important;
}

:deep(.el-button--danger:hover) {
  background-color: #c53030 !important;
  border-color: #c53030 !important;
}

:deep(.el-button--default) {
  background-color: #f7fafc !important;
  border-color: #e2e8f0 !important;
  color: #4a5568 !important;
}

:deep(.el-button--default:hover) {
  background-color: #edf2f7 !important;
  border-color: #cbd5e0 !important;
}
</style>
