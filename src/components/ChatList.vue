<template>
  <div class="chat-list-container">
    <div class="chat-messages">
      <div v-for="(message, index) in messages" :key="message.id || index" class="message-wrapper">
        <!-- 用户消息 -->
        <div v-if="getMessageRole(message) === 'user'" class="user-message">
          <div class="message-content">
            <div class="content">
              <div class="text">{{ getMessageContent(message) }}</div>
              <div class="time">{{ formatTime(getMessageTime(message)) }}</div>
            </div>
            <div class="avatar">
              <img :src="message.avatar || defaultUserAvatar" alt="用户"/>
            </div>
          </div>
        </div>

        <!-- AI消息 -->
        <div v-else-if="getMessageRole(message) === 'ai'" class="ai-message">
          <div class="message-content">
            <div class="avatar">
              <img :src="message.avatar || defaultAiAvatar" alt="AI"/>
            </div>
            <div class="content">
              <!-- 深度思考过程 (仅在有thoughtChainMessage时显示) -->
              <div v-if="hasDeepThinking(message)" class="thinking-section">
                <div class="thinking-header" @click="toggleThinking(message.id)">
                  <div class="thinking-status">
                    <div class="thinking-icon">
                      <!-- 思考中状态 -->
                      <div v-if="isThinking(message.id)" class="thinking-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                      </div>
                      <!-- 思考完成状态 -->
                      <div v-else class="thinking-completed">
                        <svg width="14" height="14" viewBox="0 0 16 16" fill="currentColor">
                          <path
                              d="M13.854 3.646a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6.5 10.293l6.646-6.647a.5.5 0 0 1 .708 0z"/>
                        </svg>
                      </div>
                    </div>
                    <span class="thinking-text">
                      {{ isThinking(message.id) ? '思考中' : '思考完成' }}
                    </span>
                  </div>
                  <div
                      v-if="isThinkingCompleted(message.id)"
                      class="expand-icon"
                      :class="{ expanded: isThinkingExpanded(message.id) }"
                  >
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                      <path
                          d="M4.427 9.573l3.396-3.396a.25.25 0 01.354 0l3.396 3.396a.25.25 0 01-.177.427H4.604a.25.25 0 01-.177-.427z"/>
                    </svg>
                  </div>
                </div>

                <!-- 思考过程详情 (只有在思考完成且展开时显示) -->
                <div v-show="isThinkingCompleted(message.id) && isThinkingExpanded(message.id)"
                     class="thinking-content">
                  <!-- 显示思考链内容 -->
                  <div v-if="getThoughtChainMessage(message)" class="thought-chain">
                    <div class="thought-chain-header">
                      <div class="chain-icon">🧠</div>
                      <div class="chain-title">深度思考过程</div>
                    </div>
                    <div class="thought-chain-content"
                         v-html="formatThoughtChain(getThoughtChainMessage(message))"></div>
                  </div>

                  <!-- 如果没有思考链内容 -->
                  <div v-else class="no-thinking-data">
                    <div class="no-data-message">暂无思考过程数据</div>
                  </div>
                </div>
              </div>

              <!-- AI回复内容 (只有在思考完成时显示) -->
              <div
                  v-if="!hasDeepThinking(message) || isThinkingCompleted(message.id)"
                  class="message-text"
                  :class="{ 'error-message': message.isError }"
                  v-html="formatContent(getMessageContent(message))"
              ></div>


              <!-- 时间戳 -->
              <div class="message-time">{{ formatTime(getMessageTime(message)) }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {ref, watch, onMounted, nextTick} from 'vue'

// Props定义
const props = defineProps({
  messages: {
    type: Array,
    default: () => []
  },
  defaultUserAvatar: {
    type: String,
    default: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
  },
  defaultAiAvatar: {
    type: String,
    default: 'https://img.freepik.com/free-vector/chatbot-chat-message-vectorart_78370-4104.jpg?semt=ais_hybrid&w=740'
  }
})

// 思考过程展开状态管理
const expandedThinking = ref(new Set())
// 思考状态管理：'thinking' | 'completed' | 'hidden'
const thinkingStates = ref(new Map())

// 先定义需要的函数
/**
 * 获取消息角色
 * @param {Object} message - 消息对象
 * @returns {string} 消息角色
 */
const getMessageRole = (message) => {
  // 适配chat接口返回的数据结构：choices[0].message.role
  if (message.choices && message.choices[0] && message.choices[0].message) {
    const role = message.choices[0].message.role
    if (role === 'assistant') return 'ai'
    if (role === 'user') return 'user'
  }
  // 适配历史记录数据结构：obj字段映射为role
  if (message.obj === 'Human') return 'user'
  if (message.obj === 'AI') return 'ai'
  // 兼容旧数据结构
  return message.role || 'ai'
}

/**
 * 获取思考链消息内容
 * @param {Object} message - 消息对象
 * @returns {string} 思考链消息内容
 */
const getThoughtChainMessage = (message) => {
  // 适配新数据结构：metadata.thoughtChainMessage
  return message.metadata?.thoughtChainMessage || message.thoughtChainMessage || ''
}

/**
 * 设置思考状态
 * @param {string} messageId - 消息ID
 * @param {string} state - 状态
 */
const setThinkingState = (messageId, state) => {
  thinkingStates.value.set(messageId, state)
}

// 监听消息变化，自动设置AI消息的思考状态
watch(() => props.messages, (newMessages) => {
  if (!newMessages) return

  newMessages.forEach(message => {
    if (getMessageRole(message) === 'ai') {
      if (message.value.startsWith('NO')) {
        message.value = message.value.slice(2).trim();
      } else if (message.value.startsWith('YES')) {
        message.value = message.value.slice(3).trim();
      }
    }
    // 如果是AI消息且有真正的思考链内容（不是空字符串），自动设置为完成状态
    const thoughtChainMessage = getThoughtChainMessage(message)

    if (getMessageRole(message) === 'ai' && thoughtChainMessage && thoughtChainMessage.trim() !== '') {
      setThinkingState(message.id, 'completed')
    }
  })
}, {immediate: true, deep: true})


/**
 * 切换思考过程的展开/收起状态 (只有在思考完成时才能展开)
 * @param {string} messageId - 消息ID
 */
const toggleThinking = (messageId) => {
  // 只有在思考完成时才能展开
  if (!isThinkingCompleted(messageId)) return

  if (expandedThinking.value.has(messageId)) {
    expandedThinking.value.delete(messageId)
  } else {
    expandedThinking.value.add(messageId)
  }
}

/**
 * 检查消息是否有深度思考过程
 * @param {Object} message - 消息对象
 * @returns {boolean} 是否有深度思考过程
 */
const hasDeepThinking = (message) => {
  // 只检查思考链消息，不处理responseData中的工作流步骤
  const thoughtChainMessage = getThoughtChainMessage(message)
  return thoughtChainMessage && thoughtChainMessage.trim() !== '' && thoughtChainMessage !== '非深度思考'
}

/**
 * 获取思考状态
 * @param {string} messageId - 消息ID
 * @returns {string} 思考状态
 */
const getThinkingState = (messageId) => {
  return thinkingStates.value.get(messageId) || 'thinking'
}

/**
 * 检查思考是否完成
 * @param {string} messageId - 消息ID
 * @returns {boolean} 是否完成
 */
const isThinkingCompleted = (messageId) => {
  return getThinkingState(messageId) === 'completed'
}

/**
 * 检查是否正在思考
 * @param {string} messageId - 消息ID
 * @returns {boolean} 是否正在思考
 */
const isThinking = (messageId) => {
  return getThinkingState(messageId) === 'thinking'
}

/**
 * 检查思考过程是否展开
 * @param {string} messageId - 消息ID
 * @returns {boolean} 是否展开
 */
const isThinkingExpanded = (messageId) => {
  return expandedThinking.value.has(messageId)
}


/**
 * 格式化数字显示
 * @param {number} num - 数字
 * @returns {string} 格式化的数字字符串
 */
const formatNumber = (num) => {
  if (!num) return '0'
  return num.toLocaleString()
}

/**
 * 格式化思考链内容
 * @param {string} thoughtChain - 思考链原始内容
 * @returns {string} 格式化后的HTML内容
 */
const formatThoughtChain = (thoughtChain) => {
  if (!thoughtChain) return ''

  let formatted = thoughtChain

  // 处理换行符
  formatted = formatted.replace(/\n/g, '<br>')

  // 处理标题（### 开头的行）
  formatted = formatted.replace(/###\s*(.+?)(<br>|$)/g, '<h4 class="thought-title">$1</h4>')

  // 处理分隔线
  formatted = formatted.replace(/---(<br>)?/g, '<div class="thought-divider"></div>')

  // 处理加粗文本（**text**）
  formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong class="thought-bold">$1</strong>')

  // 处理列表项（- 开头的行）
  formatted = formatted.replace(/^-\s*(.+?)(<br>|$)/gm, '<div class="thought-list-item">• $1</div>')

  // 处理引用标记（[id](CITE)）
  formatted = formatted.replace(/\[([^\]]+)\]\(CITE\)/g, '<span class="thought-citation">[$1]</span>')

  // 处理代码块或特殊标记
  formatted = formatted.replace(/`([^`]+)`/g, '<code class="thought-code">$1</code>')

  return formatted
}

// 暴露方法给父组件使用
defineExpose({
  setThinkingState,
  getThinkingState
})

// 滚动到底部方法
const scrollToBottom = () => {
  nextTick(() => {
    const container = document.querySelector('.chat-list-container')
    if (container) {
      container.scrollTop = container.scrollHeight
    }
  })
}

// 监听消息变化，自动滚动到底部
watch(() => props.messages, () => {
  scrollToBottom()
}, {deep: true})

// 组件挂载时也滚动到底部
onMounted(() => {
  scrollToBottom()
})

/**
 * 获取消息内容
 * @param {Object} message - 消息对象
 * @returns {string} 消息内容
 */
const getMessageContent = (message) => {
  // 适配chat接口返回的数据结构：choices[0].message.content
  if (message.choices && message.choices[0] && message.choices[0].message) {
    return message.choices[0].message.content || ''
  }
  // 适配历史记录数据结构：value字段映射为content
  return message.value || message.content || ''
}

/**
 * 获取消息时间
 * @param {Object} message - 消息对象
 * @returns {string} 消息时间
 */
const getMessageTime = (message) => {
  // 适配新数据结构：createTime字段映射为time
  return message.createTime || message.time || ''
}

/**
 * 格式化时间显示
 * @param {string} time - 时间字符串
 * @returns {string} 格式化后的时间
 */
const formatTime = (time) => {
  if (!time) return ''
  const date = new Date(time)
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

/**
 * 格式化消息内容，支持简单的Markdown语法
 * @param {string} content - 原始内容
 * @returns {string} 格式化后的HTML内容
 */
const formatContent = (content) => {
  if (!content) return ''
  return content
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/`(.*?)`/g, '<code>$1</code>')
      .replace(/\n/g, '<br>')
}
</script>

<style scoped>
/* 基础布局 - DeepSeek风格 */
.chat-list-container {
  height: 100%;
  overflow-y: auto;
  padding: 24px;
}

.chat-messages {
  max-width: 900px;
  margin: 0 auto;
}

.message-wrapper {
  margin-bottom: 32px;
}

/* 用户消息样式 - DeepSeek风格 */
.user-message .message-content {
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
  gap: 12px;
}

.user-message .avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
  border: 2px solid #e1e5e9;
}

.user-message .avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-message .content {
  max-width: 65%;
  text-align: right;
}

.user-message .text {
  background: #1976d2;
  color: white;
  padding: 12px 18px;
  border-radius: 20px;
  word-wrap: break-word;
  line-height: 1.6;
  font-size: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.user-message .time {
  font-size: 12px;
  color: #9ca3af;
  margin-top: 6px;
  font-weight: 400;
}

/* AI消息样式 - DeepSeek风格 */
.ai-message .message-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.ai-message .avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
  border: 2px solid #e1e5e9;
  background: #f8f9fa;
}

.ai-message .avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.ai-message .content {
  max-width: 75%;
  flex: 1;
}

.message-text {
  background: white;
  color: #1a1a1a;
  padding: 16px 20px;
  border-radius: 20px;
  word-wrap: break-word;
  line-height: 1.7;
  font-size: 15px;
  border: 1px solid #e1e5e9;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  margin-bottom: 8px;
}

.error-message {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.message-time {
  font-size: 12px;
  color: #9ca3af;
  margin-top: 6px;
  font-weight: 400;
}

/* 深度思考样式 - DeepSeek风格 */
.thinking-section {
  margin-bottom: 16px;
  background: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.thinking-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 14px 18px;
  cursor: pointer;
  background: #f1f3f4;
  border-bottom: 1px solid #e1e5e9;
  transition: all 0.2s ease;
}

.thinking-header:hover {
  background: #e8eaed;
}

.thinking-status {
  display: flex;
  align-items: center;
  gap: 10px;
}

.thinking-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
}

.thinking-dots {
  display: flex;
  gap: 3px;
}

.thinking-dots span {
  width: 3px;
  height: 3px;
  background: #1976d2;
  border-radius: 50%;
  animation: thinking-pulse 1.4s infinite ease-in-out;
}

.thinking-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.thinking-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes thinking-pulse {
  0%, 80%, 100% {
    transform: scale(0.6);
    opacity: 0.4;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.thinking-completed {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  color: #22c55e;
}

.thinking-text {
  font-size: 14px;
  font-weight: 500;
  transition: color 0.3s ease;
}

/* 思考中状态 */
.thinking-section:has(.thinking-dots) .thinking-text {
  color: #1976d2;
}

/* 思考完成状态 */
.thinking-section:has(.thinking-completed) .thinking-text {
  color: #22c55e;
}

.thinking-time {
  font-size: 12px;
  color: #6b7280;
  margin-left: 8px;
  font-weight: 400;
  min-width: 60px;
}

.expand-icon {
  transition: transform 0.2s ease;
  color: #6b7280;
  width: 16px;
  height: 16px;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.thinking-content {
  padding: 20px;
  background: white;
}

.thinking-step {
  margin-bottom: 20px;
  padding: 16px;
  background: #fafbfc;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  position: relative;
}

.thinking-step:last-child {
  margin-bottom: 0;
}

.thinking-step::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: #1976d2;
  border-radius: 0 2px 2px 0;
}

.step-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: #1976d2;
  border-radius: 50%;
  color: white;
  font-size: 12px;
  font-weight: 600;
}

.step-info {
  flex: 1;
}

.step-name {
  font-weight: 600;
  font-size: 15px;
  color: #1a1a1a;
  margin-bottom: 2px;
}

.step-time {
  font-size: 12px;
  color: #6b7280;
  font-weight: 400;
}

.step-details {
  margin-top: 12px;
  padding: 12px 16px;
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 6px;
}

.detail-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 13px;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-weight: 500;
  color: #6b7280;
  margin-right: 12px;
  min-width: 90px;
}

.detail-value {
  color: #1a1a1a;
  flex: 1;
  font-weight: 400;
}

/* 检索结果样式 */
.search-results {
  margin-top: 12px;
  padding: 12px 0 0 0;
  border-top: 1px solid #e1e5e9;
}

.results-title {
  font-size: 13px;
  font-weight: 500;
  color: #6b7280;
  margin-bottom: 8px;
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.result-item {
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e1e5e9;
}

.result-source {
  font-size: 12px;
  font-weight: 500;
  color: #1976d2;
  margin-bottom: 4px;
}

.result-content {
  font-size: 13px;
  color: #374151;
  line-height: 1.4;
  margin-bottom: 4px;
}

.result-score {
  font-size: 11px;
  color: #9ca3af;
}

.more-results {
  font-size: 12px;
  color: #6b7280;
  text-align: center;
  padding: 8px;
  background: #f1f3f4;
  border-radius: 4px;
  font-style: italic;
}

/* 思考链样式 */
.thought-chain {
  padding: 20px;
  background: #fafbfc;
  border-radius: 8px;
  border: 1px solid #e1e5e9;
}

.thought-chain-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e1e5e9;
}

.chain-icon {
  font-size: 20px;
}

.chain-title {
  font-size: 16px;
  font-weight: 600;
  color: #1976d2;
}

.thought-chain-content {
  line-height: 1.7;
  color: #374151;
  font-size: 14px;
}

.thought-chain-content :deep(h4.thought-title) {
  font-size: 15px;
  font-weight: 600;
  color: #1976d2;
  margin: 16px 0 8px 0;
  padding-left: 8px;
  border-left: 3px solid #1976d2;
}

.thought-chain-content :deep(.thought-divider) {
  height: 1px;
  background: linear-gradient(to right, transparent, #e1e5e9, transparent);
  margin: 16px 0;
}

.thought-chain-content :deep(.thought-bold) {
  font-weight: 600;
  color: #1a1a1a;
}

.thought-chain-content :deep(.thought-list-item) {
  margin: 6px 0;
  padding-left: 16px;
  color: #4b5563;
}

.thought-chain-content :deep(.thought-citation) {
  background: #e3f2fd;
  color: #1976d2;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  margin: 0 2px;
}

.thought-chain-content :deep(.thought-code) {
  background: #f3f4f6;
  color: #374151;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
}

.no-thinking-data {
  text-align: center;
  padding: 40px 20px;
  color: #9ca3af;
}

.no-data-message {
  font-size: 14px;
  font-style: italic;
}


/* 移动端适配 - DeepSeek风格 */
@media (max-width: 768px) {
  .chat-list-container {
    padding: 16px;
  }

  .message-wrapper {
    margin-bottom: 24px;
  }

  .user-message .content,
  .ai-message .content {
    max-width: 90%;
  }

  .user-message .avatar,
  .ai-message .avatar {
    width: 28px;
    height: 28px;
  }

  .user-message .text,
  .message-text {
    font-size: 14px;
    padding: 10px 14px;
  }

  .thinking-section {
    margin-bottom: 12px;
  }

  .thinking-header {
    padding: 12px 14px;
  }

  .thinking-content {
    padding: 16px;
  }

  .thinking-step {
    padding: 12px;
    margin-bottom: 16px;
  }

  .step-number {
    width: 20px;
    height: 20px;
    font-size: 11px;
  }

  .step-name {
    font-size: 14px;
  }

  .step-details {
    padding: 10px 12px;
  }

  .detail-row {
    font-size: 12px;
    margin-bottom: 6px;
  }

  .detail-label {
    min-width: 75px;
    margin-right: 8px;
  }

  .result-item {
    padding: 6px 10px;
  }

  .result-content {
    font-size: 12px;
  }

  .thinking-text {
    font-size: 13px;
  }

  .thinking-time {
    font-size: 11px;
  }

  .message-time {
    font-size: 11px;
  }
}
</style> 
