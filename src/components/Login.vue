<template>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <img
            alt="Logo"
            class="login-logo"
            src="https://img.freepik.com/free-vector/chatbot-chat-message-vectorart_78370-4104.jpg?semt=ais_hybrid&w=740"
        />
        <h1 class="login-title">智能回答</h1>
        <p class="login-subtitle">请登录您的账户</p>
      </div>

      <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
          @submit.prevent="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
              v-model="loginForm.username"
              :prefix-icon="User"
              clearable
              placeholder="请输入用户名"
              size="large"
          />
        </el-form-item>

        <el-form-item prop="password">
          <el-input
              v-model="loginForm.password"
              :prefix-icon="Lock"
              clearable
              placeholder="请输入密码"
              show-password
              size="large"
              type="password"
              @keyup.enter="handleLogin"
          />
        </el-form-item>

        <el-form-item>
          <el-button
              :loading="loading"
              class="login-button"
              size="large"
              type="primary"
              @click="handleLogin"
          >
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
      </el-form>

    </div>
  </div>
</template>

<script setup>
import {reactive, ref} from 'vue'
import {useRouter} from 'vue-router'
import {ElMessage} from 'element-plus'
import {Lock, User} from '@element-plus/icons-vue'
import {localAPI} from "../service/req.js";
import {resetRedirectFlag} from "../service/req.js";

const router = useRouter()

// 登录表单数据
const loginForm = reactive({
  username: '',
  password: ''
})

// 表单验证规则
const loginRules = {
  username: [
    {required: true, message: '请输入用户名', trigger: 'blur'},
    {min: 2, max: 20, message: '用户名长度在 2 到 20 个字符', trigger: 'blur'}
  ],
  password: [
    {required: true, message: '请输入密码', trigger: 'blur'},
    {min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur'}
  ]
}

const loginFormRef = ref()
const loading = ref(false)

// 登录处理
const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    await loginFormRef.value.validate()
    // 登录
    try {
      loading.value = true
      const response = await localAPI.login(loginForm.username, loginForm.password)
      console.log('登录响应:', response)

      // 由于响应拦截器返回的是response.data，所以这里直接使用response
      if (response && response.token) {
        localStorage.setItem('auth_token', response.token)
        localStorage.setItem('user_info', JSON.stringify(response.principal))
        ElMessage.success('登录成功')
        // 跳转到聊天页面
        router.push('/chat')
        resetRedirectFlag()
      } else if (response && response.data && response.data.token && response.data.principal) {
        // 兼容嵌套数据结构
        localStorage.setItem('auth_token', response.data.token)
        localStorage.setItem('user_info', JSON.stringify(response.principal))
        ElMessage.success('登录成功')
        router.push('/chat')
        resetRedirectFlag()
      } else {
        ElMessage.error('登录失败：响应数据格式错误')
      }
    } catch (err) {
      console.error('登录失败：', err)
      ElMessage.error('登录失败')
    } finally {
      loading.value = false
    }
  } catch (error) {
    // 表单验证失败
    ElMessage.error(error.response?.data?.message || '登录失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-card {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 400px;
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-logo {
  width: 64px;
  height: 64px;
  margin-bottom: 16px;
  border-radius: 12px;
}

.login-title {
  font-size: 28px;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 8px 0;
}

.login-subtitle {
  font-size: 16px;
  color: #666;
  margin: 0;
}

.login-form {
  margin-bottom: 24px;
}

.login-form :deep(.el-form-item) {
  margin-bottom: 20px;
}

.login-form :deep(.el-input__wrapper) {
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.login-form :deep(.el-input__wrapper:hover) {
  border-color: #0066ff;
  box-shadow: 0 2px 8px rgba(0, 102, 255, 0.1);
}

.login-form :deep(.el-input__wrapper.is-focus) {
  border-color: #0066ff;
  box-shadow: 0 0 0 2px rgba(0, 102, 255, 0.1);
}

.login-form :deep(.el-input__inner) {
  font-size: 16px;
  color: #1a1a1a;
}

.login-form :deep(.el-input__inner::placeholder) {
  color: #999;
}

.login-form :deep(.el-input__prefix) {
  color: #666;
}

.login-button {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
  background: #0066ff;
  border: none;
  transition: all 0.3s ease;
}

.login-button:hover {
  background: #0052cc;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 102, 255, 0.3);
}

.login-button:active {
  transform: translateY(0);
}

.login-footer {
  text-align: center;
}

.footer-text {
  font-size: 14px;
  color: #999;
  margin: 4px 0;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .login-container {
    padding: 16px;
  }

  .login-card {
    padding: 32px 24px;
    max-width: 100%;
  }

  .login-title {
    font-size: 24px;
  }

  .login-subtitle {
    font-size: 14px;
  }

  .login-logo {
    width: 56px;
    height: 56px;
  }

  .login-form :deep(.el-input__inner) {
    font-size: 16px;
  }

  .login-button {
    height: 44px;
    font-size: 16px;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .login-card {
    background: #1a1a1a;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  }

  .login-title {
    color: #ffffff;
  }

  .login-subtitle {
    color: #999;
  }

  .login-form :deep(.el-input__wrapper) {
    background: #2a2a2a;
    border-color: #404040;
  }

  .login-form :deep(.el-input__inner) {
    color: #ffffff;
    background: transparent;
  }

  .login-form :deep(.el-input__inner::placeholder) {
    color: #666;
  }

  .footer-text {
    color: #666;
  }
}
</style> 
