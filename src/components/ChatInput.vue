<template>
  <div class="chat-input-area">
    <div class="input-container">
      <div class="custom-input-wrapper">
        <textarea
          v-model="inputValue"
          class="custom-textarea"
          :placeholder="placeholder"
          :rows="rows"
          :style="{ minHeight: `calc(1.5em * ${rows} + 32px + 60px)` }"
          @keydown="handleKeydown"
          :disabled="loading"
          ref="textareaRef"
        ></textarea>
        <div class="input-actions">
          <button 
            class="thinking-toggle-btn"
            :class="{ active: isDeepThinking }"
            @click="toggleDeepThinking"
            type="button"
            :disabled="loading"
          >
            <span class="thinking-icon">🧠</span>
            深度思考
          </button>
          <button 
            class="send-btn"
            @click="handleSubmit"
            :disabled="loading || !inputValue.trim()"
            type="button"
          >
            <span v-if="loading" class="loading-spinner"></span>
            <span v-else class="send-icon">↑</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick } from 'vue'

// Props定义
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '请输入您的问题...'
  },
  rows: {
    type: Number,
    default: 3
  },
  loading: {
    type: Boolean,
    default: false
  },
  deepThinking: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

// Emits定义
const emit = defineEmits(['update:modelValue', 'submit', 'update:deepThinking'])

// 内部状态
const textareaRef = ref(null)

// 双向绑定的输入值
const inputValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 深度思考状态
const isDeepThinking = computed({
  get: () => props.deepThinking,
  set: (value) => emit('update:deepThinking', value)
})

/**
 * 切换深度思考模式
 */
const toggleDeepThinking = () => {
  if (props.loading) return
  isDeepThinking.value = !isDeepThinking.value
}

/**
 * 处理键盘事件
 * @param {KeyboardEvent} event - 键盘事件
 */
const handleKeydown = (event) => {
  // 发送键盘事件给父组件
  emit('keydown', event)
  
  // Ctrl/Cmd + Enter 发送消息
  if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
    event.preventDefault()
    handleSubmit()
  }
}

/**
 * 处理消息发送
 */
const handleSubmit = () => {
  if (props.loading || !inputValue.value.trim()) return
  emit('submit', inputValue.value.trim())
}

/**
 * 聚焦到输入框
 */
const focus = async () => {
  await nextTick()
  if (textareaRef.value) {
    textareaRef.value.focus()
  }
}

/**
 * 清空输入框
 */
const clear = () => {
  inputValue.value = ''
}

/**
 * 设置输入框内容
 * @param {string} value - 要设置的内容
 */
const setValue = (value) => {
  inputValue.value = value
}

// 暴露方法给父组件使用
defineExpose({
  focus,
  clear,
  setValue
})
</script>

<style scoped>
/* 输入区域基础样式 */
.chat-input-area {
  width: 100%;
  max-width: 700px;
  margin: 0 auto;
}

.input-container {
  width: 100%;
}

/* 自定义输入框样式 */
.custom-input-wrapper {
  position: relative;
  width: 100%;
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.custom-input-wrapper:focus-within {
  border-color: #1976d2;
  box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
}

.custom-textarea {
  width: 100%;
  padding: 16px 16px 60px 16px;
  border: none;
  outline: none;
  resize: none;
  font-size: 16px;
  line-height: 1.5;
  color: #1a1a1a;
  background: transparent;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  box-sizing: border-box;
}

.custom-textarea::placeholder {
  color: #9ca3af;
}

.custom-textarea:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.input-actions {
  position: absolute;
  bottom: 12px;
  right: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.thinking-toggle-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.thinking-toggle-btn:hover:not(:disabled) {
  background: #e9ecef;
  border-color: #d1d5db;
}

.thinking-toggle-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.thinking-toggle-btn.active {
  background: #1976d2;
  border-color: #1976d2;
  color: #e3f2fd;
}

.thinking-toggle-btn.active .thinking-icon {
  animation: thinking-pulse 2s infinite;
}

@keyframes thinking-pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.thinking-icon {
  font-size: 16px;
  transition: transform 0.2s ease;
}

.send-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: #1976d2;
  border: none;
  border-radius: 8px;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.send-btn:hover:not(:disabled) {
  background: #1565c0;
  transform: translateY(-1px);
}

.send-btn:disabled {
  background: #e0e0e0;
  cursor: not-allowed;
  transform: none;
}

.send-icon {
  font-size: 18px;
  font-weight: bold;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .custom-textarea {
    padding: 12px 12px 50px 12px;
    font-size: 16px;
  }
  
  .input-actions {
    bottom: 8px;
    right: 8px;
    gap: 6px;
  }
  
  .thinking-toggle-btn {
    padding: 6px 10px;
    font-size: 13px;
  }
  
  .thinking-icon {
    font-size: 14px;
  }
  
  .send-btn {
    width: 32px;
    height: 32px;
  }
  
  .send-icon {
    font-size: 16px;
  }
}
</style>
