<template>
  <!-- 欢迎页面 -->
  <div v-if="(isNewConversation && list.length === 0) || conversationId === null"
       class="chat-welcome-layout">
    <div class="chat-welcome-center">
      <div style="width: 80%; display: flex; flex-direction: column; align-content: center; justify-content: center">
        <div style="width: 100%;display: flex; align-content: center; justify-content: center">
          <img alt="DeepSeek Logo"
               class="chat-logo"
               src="https://img.freepik.com/free-vector/chatbot-chat-message-vectorart_78370-4104.jpg?semt=ais_hybrid&w=740"/>
        </div>
        <div class="chat-title">我是智能AI助手，很高兴见到你！</div>
        <div v-if="isNewConversation" class="chat-subtitle">
          我可以帮你回答各种问题，请把你的问题告诉我吧~
        </div>
        <div v-else class="chat-subtitle">请选择或创建对话</div>

        <!-- 新会话时显示输入框 -->
        <div v-if="isNewConversation" class="new-conversation-input">
          <ChatInput
              v-model="query"
              v-model:deep-thinking="isDeepThinking"
              :loading="loading"
              :rows="3"
              placeholder="请输入您的问题..."
              @submit="handleSubmit"
          />
        </div>
      </div>
    </div>
  </div>

  <!-- 聊天界面 -->
  <div v-else class="chat-container">
    <div class="chat-content">
      <!-- 使用ChatList组件 -->
      <ChatList
        ref="chatListRef"
        :messages="list"
        :default-user-avatar="userAvatar"
        :default-ai-avatar="aiAvatar"
      />
      <div class="bottom-input">
        <ChatInput
          v-model="query"
          v-model:deep-thinking="isDeepThinking"
          :loading="loading"
          :rows="3"
          placeholder="请输入您的问题..."
          @submit="handleSubmit"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import {computed, nextTick, ref, watch, onMounted} from 'vue'
import {localAPI} from '../service/req'
import {ElMessage} from "element-plus"
import ChatList from './ChatList.vue'
import ChatInput from './ChatInput.vue'


const props = defineProps({
  conversationId: {
    type: String,
    default: null
  },
  isNewConversation: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['create-conversation', 'refresh-conversations', 'set-conversation-id'])

const list = ref([])
const query = ref('')
const loading = ref(false)
const isChatting = ref(false) // 跟踪是否正在聊天中
const isDeepThinking = ref(false) // 深度思考模式开关
const chatListRef = ref(null) // ChatList组件引用

// 头像配置
const userAvatar = ref('https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png')
const aiAvatar = ref('https://img.freepik.com/free-vector/chatbot-chat-message-vectorart_78370-4104.jpg?semt=ais_hybrid&w=740')

watch([() => props.conversationId, () => props.isNewConversation], async ([newId, isNew], [oldId, oldIsNew]) => {
  // 避免在聊天过程中重新加载
  if (isNew && !oldIsNew) {
    list.value = []
  } else if (newId && newId !== oldId && !isChatting.value) {
    await loadConversationFromAPI(newId)
  }
}, {immediate: true, deep: true})



async function loadConversationFromAPI(chatId) {
  try {
    const response = await localAPI.getChatRecords(chatId, 0, 100)
    console.log('API响应:', response)

    // 处理API返回结构
    if (response && response.code === 200 && response.data) {
      list.value = response.data
      console.log('list', list.value)
    }
  } catch (err) {
    console.error('加载聊天记录失败：', err)
    ElMessage.error('加载聊天记录失败')
    list.value = []
  }
}

/**
 * 处理消息发送
 * @param {string} message - 要发送的消息内容
 */
function handleSubmit(message) {
  if (!message || !message.trim()) return; // 防止空消息

  loading.value = true
  isChatting.value = true // 标记开始聊天

  // 添加用户消息到聊天记录
  const userMessage = {
    id: `user-${Date.now()}`,
    role: 'user',
    content: message,
    time: new Date().toISOString()
  }

  list.value.push(userMessage)
  // 清空输入框
  query.value = ''

  // 发送请求并处理响应
  sendQuery(message)
}

/**
 * 发送查询请求
 * @param {string} userQuery - 用户查询内容
 */
const sendQuery = async (userQuery) => {
  try {
    // 构建本地API请求
    const messages = []
    // 添加当前用户消息
    messages.push({
      role: 'user',
      content: userQuery
    });

    // 使用当前会话ID或生成新的
    const chatId = props.conversationId || (Date.now().toString() + Math.random().toString(36).substr(2, 9));

    // 根据深度思考模式调整请求参数
    const requestOptions = {
      chatId,
      messages,
      isDeepThinking: isDeepThinking.value // 传递深度思考模式状态
    };

    console.log('发送请求:', requestOptions);
    const res = await localAPI.sendChatMessage(chatId, messages, isDeepThinking.value);
    console.log('聊天API响应:', res);

    // 处理新的详细响应格式
    let aiResponse = ''
    let thoughtChainMessage = ''
    
    // 安全地获取AI响应内容
    if (res.choices && res.choices[0] && res.choices[0].message) {
      if (res.choices[0].message.content.startsWith('NO')) {
        aiResponse = res.choices[0].message.content.slice(2).trim()
      } else if (res.choices[0].message.content.startsWith('YES')) {
        aiResponse = res.choices[0].message.content.slice(3).trim()
      }
    } else if (res.content) {
      aiResponse = res.content
    } else {
      aiResponse = '抱歉，我无法生成回复。'
    }
    
    // 获取思考链消息
    if (res.thoughtChainMessage) {
      thoughtChainMessage = res.thoughtChainMessage
    }

    console.log("thoughtChainMessage", thoughtChainMessage)
    
    const aiMessageId = `ai-${Date.now()}`
    const aiMessage = {
      id: aiMessageId,
      role: 'ai',
      content: aiResponse,
      time: new Date().toISOString(),
      thoughtChainMessage: thoughtChainMessage
    };

    // 先添加消息到列表（思考中状态）
    list.value.push(aiMessage);

    // 强制更新DOM以确保消息显示
    await nextTick()

    // 如果有深度思考过程，模拟思考完成
    if (isDeepThinking.value) {
      // 模拟思考时间
      setTimeout(() => {
        if (chatListRef.value) {
          console.log(111)
          chatListRef.value.setThinkingState(aiMessageId, 'completed')
        }
      }, 2000) // 2秒后思考完成
    }

    // 如果是新会话，需要刷新会话列表并设置当前会话ID
    if (!props.conversationId) {
      // 触发父组件刷新会话列表
      emit('refresh-conversations')
      // 设置当前会话ID，这样后续消息会使用同一个会话
      emit('set-conversation-id', chatId)
    }
  } catch (err) {
    console.error('请求失败：', err);
    console.error('错误详情：', err.response?.data || err.message);
    
    // 显示具体的错误信息
    let errorMessage = '请求出错，请检查网络连接或API配置';
    if (err.response?.data?.message) {
      errorMessage = err.response.data.message;
    } else if (err.message) {
      errorMessage = err.message;
    }
    
    ElMessage.error(errorMessage);
    
    // 在出错时也添加一个错误消息到聊天记录
    const errorMessageObj = {
      id: `error-${Date.now()}`,
      role: 'ai',
      content: errorMessage,
      time: new Date().toISOString(),
      isError: true
    }
    list.value.push(errorMessageObj)
    
    // 强制更新DOM并滚动到底部
    await nextTick()
    if (chatListRef.value) {
      chatListRef.value.scrollToBottom()
    }
  } finally {
    query.value = '';
    loading.value = false;
    isChatting.value = false; // 重置聊天状态
  }
}
</script>

<style scoped>
/* 欢迎布局 */
.chat-welcome-layout {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  background: #fff;
  text-align: center;
}

.chat-welcome-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 0 20px;
}

.chat-logo {
  width: 72px;
  height: 72px;
  margin-bottom: 24px;
}

.chat-title {
  font-size: 2rem;
  font-weight: 700;
  color: #222;
  margin-bottom: 12px;
}

.chat-subtitle {
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 36px;
}

/* 聊天容器布局 */
.chat-container {
  height: 100%;
  width: 100%;
  background: #fff;
}

.chat-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
}



/* 输入区域 */
.new-conversation-input {
  padding: 20px;
  background: white;
}

.bottom-input {
  position: sticky;
  bottom: 0;
  z-index: 10;
  background: white;
  padding: 20px;
}



/* 移动端适配 */
@media (max-width: 768px) {
  .chat-welcome-center {
    margin-top: 0;
  }
  
  .chat-logo {
    width: 60px;
    height: 60px;
    margin-bottom: 20px;
  }
  
  .chat-title {
    font-size: 1.5rem;
    margin-bottom: 10px;
  }
  
  .chat-subtitle {
    font-size: 1rem;
    margin-bottom: 24px;
  }
  
  .chat-content {
    padding: 16px 10px;
  }
  
  .bottom-input {
    padding-top: 12px;
    margin-top: 8px;
  }
  
  .custom-textarea {
    min-height: 60px;
    padding: 12px 12px 50px 12px;
    font-size: 16px;
  }

  .input-actions {
    bottom: 8px;
    right: 8px;
    gap: 6px;
  }

  .thinking-toggle-btn {
    padding: 6px 10px;
    font-size: 13px;
  }

  .thinking-icon {
    font-size: 14px;
  }

  .send-btn {
    width: 32px;
    height: 32px;
  }

  .send-icon {
    font-size: 16px;
  }
}
</style>
