import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

// https://vite.dev/config/
export default defineConfig({
  server: {
    port: 5170,
    host: true,
    open: true,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        // target: 'http://http://***********:8080',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  },
  plugins: [vue()]
})
