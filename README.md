# FastGPT 聊天前端

这是一个基于 Vue 3 的聊天应用前端，集成了 FastGPT API。

## 功能特性

- 🤖 集成 FastGPT AI 聊天功能
- 💬 多会话管理
- 💾 本地聊天记录存储
- 🎨 现代化的用户界面
- 📱 响应式设计

## 快速开始

### 安装依赖

```bash
npm install
```

### 配置 API

项目已经配置了FastGPT API密钥。如果需要修改，请编辑 `src/config/api.js` 文件：

```javascript
export const API_CONFIG = {
  // FastGPT API 基础URL
  BASE_URL: 'https://api.fastgpt.in/api/v1',
  
  // API密钥
  API_KEY: 'your-api-key-here', // 替换为您的API密钥
  
  // 请求头设置
  getHeaders() {
    return {
      'Authorization': `Bearer ${this.API_KEY}`,
      'Content-Type': 'application/json'
    };
  }
};
```

### 启动开发服务器

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

## 项目结构

```
src/
├── components/
│   ├── chat.vue          # 聊天组件
│   └── Layout.vue        # 布局组件
├── config/
│   └── api.js           # API 配置文件
├── service/
│   └── req.js           # HTTP 请求服务
└── App.vue              # 主应用组件
```

## API 配置说明

### FastGPT API 端点

- **基础 URL**: `https://api.fastgpt.in/api/v1`
- **聊天接口**: `/chat/completions`

### 请求格式

```javascript
{
  "chatId": "unique-chat-id",
  "stream": false,
  "detail": false,
  "messages": [
    {
      "role": "user",
      "content": "用户消息内容"
    }
  ]
}
```

### 请求头

```javascript
{
  "Authorization": "Bearer your-api-key",
  "Content-Type": "application/json"
}
```

### 参数说明

- `chatId`: 唯一的聊天会话ID
- `stream`: 是否使用流式响应
- `detail`: 是否返回详细信息
- `messages`: 消息数组，包含对话历史

### 支持的模型

- `gpt-3.5-turbo` (默认)
- `gpt-4`
- 其他 FastGPT 支持的模型

## 本地存储

应用使用浏览器的 localStorage 来保存：

- 聊天记录 (`chat_history_${conversationId}`)
- 会话列表 (`conversations`)

## 注意事项

1. **API 密钥**: 如果您的 FastGPT 实例需要 API 密钥，请在配置文件中设置
2. **CORS**: 确保您的 FastGPT 实例允许跨域请求
3. **速率限制**: 注意 FastGPT API 的速率限制
4. **数据隐私**: 聊天记录仅存储在本地浏览器中

## 故障排除

### 常见问题

1. **API 请求失败**
   - 检查网络连接
   - 验证 API 密钥是否正确
   - 确认 FastGPT 服务是否正常运行

2. **聊天记录丢失**
   - 检查浏览器是否启用了 localStorage
   - 确认没有清除浏览器数据

3. **界面显示异常**
   - 确保所有依赖已正确安装
   - 检查浏览器控制台是否有错误信息

## 技术栈

- Vue 3
- Element Plus
- Axios
- Vite

## 许可证

MIT License
